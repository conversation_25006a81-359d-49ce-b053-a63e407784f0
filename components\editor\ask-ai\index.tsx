"use client";
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useRef, useCallback, useEffect, useMemo } from "react";
import classNames from "classnames";
import { toast } from "sonner";
import { useLocalStorage, useUpdateEffect } from "react-use";
import { ChevronDown, Sparkles, ArrowUp, ImagePlus, Figma } from "lucide-react";
import { FaStopCircle } from "react-icons/fa";

import { defaultHTML } from "@/lib/consts";
import ProModal from "@/components/pro-modal";
import { Button } from "@/components/ui/button";
import { ModelSelector } from "@/components/ui/model-selector";
import { MODELS } from "@/lib/providers";
import { HtmlHistory } from "@/types";
import { LoginModal } from "@/components/login-modal";
import Loading from "@/components/loading";
import { SelectedHtmlElement } from "./selected-html-element";
import FigmaIntegration from "@/components/editor/figma-integration";

// 🔧 增强编辑功能：上下文分析工具函数
const getElementSelector = (element: HTMLElement): string => {
  const path: string[] = [];
  let current: Element | null = element;
  
  while (current && current !== document.body) {
    let selector = current.tagName.toLowerCase();
    
    if (current.id) {
      selector += `#${current.id}`;
      path.unshift(selector);
      break;
    }
    
    if (current.className) {
      const classes = current.className.trim().split(/\s+/).filter(cls => !cls.includes('hovered'));
      if (classes.length > 0) {
        selector += `.${classes.join('.')}`;
      }
    }
    
    // 添加位置信息（如果有同类型兄弟元素）
    const siblings = Array.from(current.parentElement?.children || [])
      .filter(sibling => sibling.tagName === current!.tagName);
    if (siblings.length > 1) {
      const index = siblings.indexOf(current) + 1;
      selector += `:nth-of-type(${index})`;
    }
    
    path.unshift(selector);
    current = current.parentElement;
  }
  
  return path.join(' > ');
};

const analyzeElementType = (element: HTMLElement): string => {
  const tagName = element.tagName.toLowerCase();
  const className = element.className || '';
  
  // 🚨 检查是否为动态生成的元素
  if (element.hasAttribute('data-zr-dom-id') || 
      className.includes('echarts') || 
      tagName === 'canvas' && element.getAttribute('width')) {
    return '动态图表元素 (不可编辑)';
  }
  
  const text = element.textContent?.trim() || '';
  const hasText = text.length > 0;
  const hasChildren = element.children.length > 0;
  
  // 分析元素类型
  if (tagName === 'h1' || tagName === 'h2' || tagName === 'h3' || tagName === 'h4' || tagName === 'h5' || tagName === 'h6') {
    return `标题元素 (${tagName.toUpperCase()})`;
  }
  if (tagName === 'p') return '段落文本';
  if (tagName === 'span' || tagName === 'div') {
    if (hasText && !hasChildren) return '文本内容';
    if (hasChildren) return '容器元素';
    return '空容器';
  }
  if (tagName === 'button') return '按钮元素';
  if (tagName === 'a') return '链接元素';
  if (tagName === 'img') return '图片元素';
  if (tagName === 'input') return '输入框';
  if (tagName === 'table') return '表格';
  if (tagName === 'td' || tagName === 'th') return '表格单元格';
  if (tagName === 'li') return '列表项';
  if (tagName === 'nav') return '导航栏';
  if (tagName === 'header') return '页头';
  if (tagName === 'footer') return '页脚';
  if (['svg', 'canvas'].includes(tagName)) return '图形元素';
  
  return hasText ? '文本元素' : '布局元素';
};

const getSiblingContext = (element: HTMLElement): Array<{tag: string, text: string, type: string}> => {
  const siblings = Array.from(element.parentElement?.children || [])
    .filter(sibling => sibling !== element)
    .slice(0, 3) // 只取前3个兄弟元素
    .map(sibling => ({
      tag: sibling.tagName.toLowerCase(),
      text: (sibling.textContent?.trim() || '').substring(0, 30),
      type: analyzeElementType(sibling as HTMLElement)
    }));
  
  return siblings;
};

const getParentContext = (element: HTMLElement): {tag: string, type: string, role: string} | null => {
  const parent = element.parentElement;
  if (!parent || parent === document.body) return null;
  
  const tag = parent.tagName.toLowerCase();
  const type = analyzeElementType(parent);
  
  // 分析父元素的作用
  let role = '容器';
  if (['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'].includes(tag)) {
    role = '页面区块';
  } else if (['table', 'ul', 'ol', 'dl'].includes(tag)) {
    role = '数据结构';
  } else if (['form', 'fieldset'].includes(tag)) {
    role = '表单组件';
  } else if (parent.className.includes('card') || parent.className.includes('panel')) {
    role = '卡片组件';
  }
  
  return { tag, type, role };
};

export function AskAI({
  inputPrompt = "",
  shouldSubmitOnLoad = false,
  setInputPrompt,
  error = "",
  setError,
  onAskAI,
  selectedElement,
  setSelectedElement,
  isStandalone = false,
  isWelcomeMode = false,
  placeholder: customPlaceholder,
  onModeToggle,
  showModeToggle = false,
}: {
  inputPrompt?: string;
  shouldSubmitOnLoad?: boolean;
  setInputPrompt?: (prompt: string) => void;
  error?: string;
  setError?: (error: string) => void;
  onAskAI?: (prompt: string, model: string, images?: string[]) => void;
  selectedElement?: HTMLElement | null;
  setSelectedElement?: (element: HTMLElement | null) => void;
  isStandalone?: boolean;
  isWelcomeMode?: boolean;
  placeholder?: string;
  onModeToggle?: () => void;
  showModeToggle?: boolean;
}) {
  // 🚨 简化状态管理 - 确保输入正常工作
  const [internalInput, setInternalInput] = useState("");
  const [model] = useLocalStorage("model", "deepseek-chat");
  
  // 🔧 修复SSR水合错误：使用isMounted确保客户端一致性
  const [isMounted, setIsMounted] = useState(false);
  const [storedModel, setStoredModel] = useLocalStorage("selectedModel", "loomrun-1.2ds");
  const [selectedModel, setSelectedModel] = useState("loomrun-1.2ds");
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showProModal, setShowProModal] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [isFigmaModalOpen, setIsFigmaModalOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [textareaHeight, setTextareaHeight] = useState(0);
  
  // 🎯 判断是否在项目内页面（非欢迎页面）
  const isInProject = !isWelcomeMode && !isStandalone;
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 🎯 打字机效果状态 - 保留以展示应用功能
  const [currentPlaceholder, setCurrentPlaceholder] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [charIndex, setCharIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [typingSpeed, setTypingSpeed] = useState(50);
  const [showCursor, setShowCursor] = useState(true);
  const [isTypingActive, setIsTypingActive] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  
  // 🎯 简洁专业的动态占位符文本列表 - 使用useMemo避免重新创建
  const placeholderTexts = React.useMemo(() => [
    "告诉我你想要什么...",
    "创建网站...", 
    "制作应用...",
    "设计PPT...",
    "开发游戏...",
    "构建工具...",
    "营销页面...",
    "管理系统...",
    "电商平台...",
    "博客网站...",
    "数据面板...",
    "学习平台...",
    "企业官网...",
    "社交平台...",
    "移动应用..."
  ], []);

  // 🚨 关键修复：统一的输入值获取
  const currentInputValue = setInputPrompt ? inputPrompt : internalInput;

  // 🔧 修复SSR水合错误：组件挂载后同步localStorage
  useEffect(() => {
    setIsMounted(true);
    if (storedModel) {
      setSelectedModel(storedModel);
    }
  }, [storedModel]);

  // 🔧 同步selectedModel到localStorage
  useEffect(() => {
    if (isMounted) {
      setStoredModel(selectedModel);
    }
  }, [selectedModel, isMounted, setStoredModel]);

  // 🔧 修复：设置正确的初始textarea高度
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const minHeight = isWelcomeMode ? 24 : 20;
      // 如果输入框为空，直接设置为最小高度
      if (!currentInputValue.trim()) {
        textarea.style.height = minHeight + 'px';
      } else {
        // 如果有内容，重新计算高度
        textarea.style.height = 'auto';
        const scrollHeight = textarea.scrollHeight;
        const lineHeight = isWelcomeMode ? 28 : 20;
        const maxHeight = 8 * lineHeight;
        const finalHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
        textarea.style.height = finalHeight + 'px';
      }
    }
  }, [isWelcomeMode, currentInputValue]);

  // 🎨 光标闪烁效果 - 修复：用户交互时停止光标闪烁
  useEffect(() => {
    if (!isWelcomeMode || !isTypingActive || hasUserInteracted) {
      setShowCursor(false); // 立即隐藏光标
      return;
    }

    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 530);

    return () => clearInterval(cursorInterval);
  }, [isWelcomeMode, isTypingActive, hasUserInteracted]);

  // 🎯 用户交互时停止打字机效果 - 修复：同时清除光标
  const handleUserInteraction = useCallback(() => {
    if (isWelcomeMode && isTypingActive) {
      setIsTypingActive(false);
      setCurrentPlaceholder("");
      setShowCursor(false); // 立即隐藏虚拟光标
      setHasUserInteracted(true);
    }
  }, [isWelcomeMode, isTypingActive]);

  // 🚀 优化：用户点击或聚焦时立即停止打字机效果
  const handleTextareaFocus = useCallback(() => {
    handleUserInteraction();
  }, [handleUserInteraction]);

  const handleTextareaClick = useCallback(() => {
    handleUserInteraction();
  }, [handleUserInteraction]);

  // 🚨 核心修复：简化输入处理逻辑 + 动态扩展
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;

    // 🎯 用户开始输入时立即停止打字机效果
    handleUserInteraction();

    // 立即更新输入值
    if (setInputPrompt) {
      setInputPrompt(newValue);
    } else {
      setInternalInput(newValue);
    }

    // 🚀 优化：立即清空占位符，无需等待
    if (isWelcomeMode && newValue.length > 0) {
      setCurrentPlaceholder("");
      setIsTypingActive(false);
    }
    
    // Auto-resize with dynamic expansion
    const textarea = e.target;
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    // 根据模式设置不同的行高和最大高度
    const lineHeight = isWelcomeMode ? 28 : 20; // 欢迎模式28px，普通模式20px
    const maxHeight = 8 * lineHeight; // 8行的高度
    
    // 记录当前高度
    setTextareaHeight(scrollHeight);
    
    // 动态扩展逻辑
    if (uploadedImages.length > 0) {
      // 有图片时，检查是否需要扩展
      const shouldExpand = scrollHeight > maxHeight * 0.6; // 超过60%高度时开始扩展
      if (shouldExpand !== isExpanded) {
        setIsExpanded(shouldExpand);
      }
    }
    
    // 设置最小高度，确保单行输入不会太高
    const minHeight = isWelcomeMode ? 24 : 20; // 24px for welcome, 20px for normal
    const finalHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
    textarea.style.height = finalHeight + 'px';
  };

  // 🎯 输入框聚焦时停止打字机效果


  // 键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 🎯 任何按键都停止打字机效果
    handleUserInteraction();
    
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // 处理图片文件 - 在项目内页面禁用
  const processImageFiles = useCallback((files: FileList | File[]) => {
    // 🚫 在项目内页面禁用图片上传
    if (isInProject) {
      toast.error("项目内页面不支持图片上传，请在新建项目时使用图片功能");
      return;
    }
    
    const fileArray = Array.from(files);
    const imageFiles = fileArray.filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length > 0) {
      setUploadedImages(prev => [...prev, ...imageFiles]);
      
      // 创建图片预览URL
      const newUrls = imageFiles.map(file => URL.createObjectURL(file));
      setImageUrls(prev => [...prev, ...newUrls]);
      
      // 🎯 自动切换到图像理解模型 (1.6db)
      if (selectedModel !== "loomrun-1.6db") {
        setSelectedModel("loomrun-1.6db");
        console.log('🔄 自动切换到豆包模型:', {
          from: selectedModel,
          to: "loomrun-1.6db",
          imageCount: imageFiles.length
        });
        toast.success(`已添加 ${imageFiles.length} 张图片，已切换到图像理解模型`);
      } else {
        toast.success(`已添加 ${imageFiles.length} 张图片`);
      }
    } else {
      toast.error("请选择图片文件");
    }
  }, [isInProject, selectedModel, setSelectedModel]);

  // 图片上传处理 - 在项目内页面禁用
  const handleImageUpload = useCallback(() => {
    if (isInProject) {
      toast.error("项目内页面不支持图片上传，请在新建项目时使用图片功能");
      return;
    }
    fileInputRef.current?.click();
  }, [isInProject]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      processImageFiles(files);
    }
    
    // 重置文件输入
    if (e.target) {
      e.target.value = '';
    }
  }, [processImageFiles]);

  // Figma导入功能
  const handleFigmaImport = useCallback(() => {
    setIsFigmaModalOpen(true);
  }, []);

  // 移除图片
  const handleRemoveImage = useCallback((index: number) => {
    // 清理URL对象
    if (imageUrls[index]) {
      URL.revokeObjectURL(imageUrls[index]);
    }
    
    const newImages = uploadedImages.filter((_, i) => i !== index);
    const newUrls = imageUrls.filter((_, i) => i !== index);
    
    setUploadedImages(newImages);
    setImageUrls(newUrls);
    
    // 🎯 如果移除了所有图片，且当前是图像理解模型，自动切换回文本对话模型
    if (newImages.length === 0 && selectedModel === "loomrun-1.6db") {
      setSelectedModel("loomrun-1.2ds");
      toast.success("图片已移除，已切换回文本对话模型");
    } else {
      toast.success("图片已移除");
    }
  }, [imageUrls, uploadedImages, selectedModel, setSelectedModel]);

  // 拖拽处理 - 在项目内页面禁用
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!isInProject) {
      setIsDragging(true);
    }
  }, [isInProject]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!containerRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (isInProject) {
      toast.error("项目内页面不支持图片上传，请在新建项目时使用图片功能");
      return;
    }
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processImageFiles(files);
    }
  }, [isInProject, processImageFiles]);

  // 复制粘贴处理 - 在项目内页面禁用
  const handlePaste = useCallback((e: React.ClipboardEvent) => {
    const items = e.clipboardData.items;
    const files: File[] = [];
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (file) {
          files.push(file);
        }
      }
    }
    
    if (files.length > 0) {
      if (isInProject) {
        toast.error("项目内页面不支持图片上传，请在新建项目时使用图片功能");
        return;
      }
      processImageFiles(files);
    }
  }, [isInProject, processImageFiles]);

  // 快捷提示词 - 根据LoomRun系统功能优化
  const quickPrompts = [
    "生成跟图片相似的网页应用",
    "生成跟图片相似的手机应用"
  ];

  // 点击快捷提示词
  const handleQuickPrompt = useCallback((prompt: string) => {
    const newValue = currentInputValue ? `${currentInputValue}\n${prompt}` : prompt;
    if (setInputPrompt) {
      setInputPrompt(newValue);
    } else {
      setInternalInput(newValue);
    }
    
    // 自动调整textarea高度和扩展状态
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const scrollHeight = textareaRef.current.scrollHeight;
      textareaRef.current.style.height = `${scrollHeight}px`;
      
      // 更新扩展状态
      if (uploadedImages.length > 0) {
        const maxHeight = 8 * 24;
        const shouldExpand = scrollHeight > maxHeight * 0.6 || newValue.length > 100;
        setIsExpanded(shouldExpand);
      }
    }
    }, [currentInputValue, setInputPrompt, uploadedImages.length]);

  // 清理图片URL，防止内存泄漏
  useEffect(() => {
    return () => {
      imageUrls.forEach(url => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  // 监听文本变化，自动调整扩展状态
  useEffect(() => {
    if (uploadedImages.length === 0) {
      setIsExpanded(false);
      return;
    }

    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = 8 * 24; // 8行的高度
      
      // 根据文本长度和高度决定是否扩展
      const shouldExpand = scrollHeight > maxHeight * 0.6 || currentInputValue.length > 100;
      
      if (shouldExpand !== isExpanded) {
        setIsExpanded(shouldExpand);
      }
    }
  }, [currentInputValue, uploadedImages.length, isExpanded]);
   
  // 🎨 优化的打字机效果初始化
  useEffect(() => {
    if (isWelcomeMode && currentInputValue.length === 0 && !hasUserInteracted) {
      // 🚀 只在用户未交互且输入框为空时启动
      const delay = 300; // 减少延迟时间

      const timer = setTimeout(() => {
        if (currentInputValue.length === 0) { // 双重检查
          setIsTypingActive(true);
          setCurrentIndex(0);
          setCharIndex(0);
          setIsDeleting(false);
          setCurrentPlaceholder("");
        }
      }, delay);

      return () => clearTimeout(timer);
    } else {
      // 🚀 立即停止打字机效果 - 修复：同时清除光标
      setIsTypingActive(false);
      setShowCursor(false); // 立即隐藏虚拟光标
      if (currentInputValue.length > 0 || hasUserInteracted) {
        setCurrentPlaceholder("");
      }
    }
  }, [isWelcomeMode, currentInputValue.length, hasUserInteracted]);

  // 🎨 打字机效果 - 使用useEffect确保初始化时也能运行
  useEffect(() => {
    if (!isTypingActive) {
      return;
    }

    const currentText = placeholderTexts[currentIndex];
    let timeout: NodeJS.Timeout;

    if (!isDeleting && charIndex < currentText.length) {
      timeout = setTimeout(() => {
        setCurrentPlaceholder(currentText.slice(0, charIndex + 1));
        setCharIndex(prev => prev + 1);
        
        // 🚀 优化打字速度 - 更快更流畅
        const baseSpeed = 30; // 减少基础延迟
        const variation = Math.random() * 15; // 减少随机变化
        const pauseChance = Math.random();

        if ([' ', '，', '。', '、', '...'].includes(currentText[charIndex])) {
          setTypingSpeed(baseSpeed + variation + 30); // 减少标点符号延迟
        } else if (pauseChance < 0.02) { // 减少暂停频率
          setTypingSpeed(baseSpeed + variation + 50);
        } else {
          setTypingSpeed(baseSpeed + variation);
        }
      }, typingSpeed);
      
    } else if (!isDeleting && charIndex === currentText.length) {
      // 🚀 减少完成后的等待时间
      timeout = setTimeout(() => {
        setIsDeleting(true);
      }, 800 + Math.random() * 400);

    } else if (isDeleting && charIndex > 0) {
      timeout = setTimeout(() => {
        setCurrentPlaceholder(currentText.slice(0, charIndex - 1));
        setCharIndex(prev => prev - 1);

        // 🚀 优化删除速度
        const baseDeleteSpeed = 8; // 更快的删除速度
        const deleteVariation = Math.random() * 4;
        const hesitationChance = Math.random();

        if (hesitationChance < 0.01) { // 减少犹豫频率
          setTypingSpeed(baseDeleteSpeed + deleteVariation + 20);
        } else {
          setTypingSpeed(baseDeleteSpeed + deleteVariation);
        }
      }, typingSpeed);

    } else if (isDeleting && charIndex === 0) {
      // 🚀 减少切换到下一个文本的延迟
      timeout = setTimeout(() => {
        setIsDeleting(false);
        setCurrentIndex(prev => (prev + 1) % placeholderTexts.length);
        setCharIndex(0);
        setTypingSpeed(30);
      }, 100 + Math.random() * 200);
    }

    return () => clearTimeout(timeout);
  }, [currentIndex, charIndex, isDeleting, typingSpeed, isTypingActive, placeholderTexts]);

  // 显示的placeholder
  const displayPlaceholder = isWelcomeMode 
    ? (customPlaceholder || `${currentPlaceholder}${showCursor ? '|' : ''}`)
    : (customPlaceholder || "");

  const handleSubmit = async () => {
    if (isGenerating || !currentInputValue.trim()) return;

    // 🎯 保存原始输入
    const originalInput = currentInputValue;

    setIsGenerating(true);
    if (setError) setError("");

    try {
      // 🎯 关键修复：在发送前先检查积分，避免消息被发送出去
      if (isWelcomeMode) {
        console.log('🔍 AskAI: 欢迎模式，积分检查将在WelcomeLayout中处理');
      } else {
        // 🔍 编辑器模式：在这里进行积分预检查
        console.log('🔍 AskAI: 编辑器模式，进行积分预检查');

        // 模型映射
        const modelMapping = {
          "loomrun-1.2ds": "deepseek-chat",
          "loomrun-1.6db": "doubao-seed-1-6-250615"
        };

        const actualModel = selectedModel && modelMapping[selectedModel as keyof typeof modelMapping]
          ? modelMapping[selectedModel as keyof typeof modelMapping]
          : "deepseek-chat";

        // 发送预检查请求
        const preCheckResponse = await fetch('/api/ask-ai', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: originalInput,
            provider: actualModel.includes('doubao') ? 'doubao-official' : 'deepseek-official',
            model: actualModel,
            images: uploadedImages || [],
            preCheckOnly: true
          }),
        });

        // 🎯 处理积分不足情况 - 直接阻止发送
        if (preCheckResponse.status === 402) {
          const errorData = await preCheckResponse.json();
          console.log('⚠️ AskAI: 积分不足，阻止发送', {
            pointsRequired: errorData.pointsRequired,
            currentPoints: errorData.currentPoints
          });

          // 🎯 友好的积分不足提示 - 使用信息提示而不是错误
          const friendlyMessage = `需要 ${errorData.pointsRequired} 积分，当前余额 ${errorData.currentPoints} 积分`;
          toast.info(`💰 ${friendlyMessage}`, {
            duration: 6000,
            action: {
              label: '立即充值',
              onClick: () => window.open('/points-pro', '_blank')
            }
          });

          // 🎯 关键：消息保留在输入框中，不清空，不发送
          setIsGenerating(false);
          return;
        }

        if (!preCheckResponse.ok) {
          throw new Error(`积分检查失败: ${preCheckResponse.status}`);
        }

        console.log('✅ AskAI: 积分检查通过，继续发送消息');
      }

      if (onAskAI) {
        // 构建增强提示（不包含图片文本，图片将单独传递）
        const enhancedPrompt = currentInputValue;
        
        // 构建模型映射 - 使用selectedModel而不是model
        const modelMapping = {
          "loomrun-1.2ds": "deepseek-chat",
          "loomrun-1.6db": "doubao-seed-1-6-250615"
        };
        
        const actualModel = modelMapping[selectedModel as keyof typeof modelMapping] || "deepseek-chat";
        
        console.log('🚀 AskAI提交:', {
          selectedModel: selectedModel,
          actualModel,
          hasImages: uploadedImages.length > 0,
          imageCount: uploadedImages.length,
          prompt: enhancedPrompt.substring(0, 50) + '...'
        });
        
        // 🔧 修复重复项目创建问题：如果有图片，等待图片转换完成后再发送请求
        if (uploadedImages.length > 0) {
          console.log('🖼️ 检测到图片，等待转换完成后发送请求');
          
          try {
            const imageBase64Array: string[] = [];
            for (const image of uploadedImages) {
              const base64 = await new Promise<string>((resolve) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.readAsDataURL(image);
              });
              imageBase64Array.push(base64);
            }
            
            console.log('🖼️ 图片转换完成，发送带图片的请求', {
              imageCount: imageBase64Array.length
            });
            
            // 发送带图片的请求
            await onAskAI(enhancedPrompt, selectedModel || "loomrun-1.2ds", imageBase64Array);
          } catch (imageError) {
            console.error('❌ 图片转换失败:', imageError);
            toast.error('图片处理失败');
            setIsGenerating(false);
            return;
          }
        } else {
          // 没有图片时直接发送请求
          await onAskAI(enhancedPrompt, selectedModel || "loomrun-1.2ds", []);
        }

        // 🎯 关键修复：只有在onAskAI成功执行后才清空输入框
        console.log('✅ AskAI: onAskAI执行成功，清空输入框');

        // 清空输入和重置状态
        if (setInputPrompt) {
          setInputPrompt("");
        } else {
          setInternalInput("");
        }

        // 清理图片URL
        imageUrls.forEach(url => URL.revokeObjectURL(url));
        setUploadedImages([]);
        setImageUrls([]);

        // 重置高度
        if (textareaRef.current) {
          textareaRef.current.style.height = 'auto';
        }
      }
      
    } catch (error: any) {
      console.error('AI request error:', error);

      // 🔍 友好的错误处理
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          toast.info("AI 请求已取消");
        } else if (error.message?.includes('fetch') || error.message?.includes('network')) {
          toast.error("网络连接失败，请检查网络后重试");
          if (setError) setError("网络连接失败，请检查网络后重试");
        } else {
          toast.error(error.message || "请求失败，请重试");
          if (setError) setError(error.message || "请求失败，请重试");
        }
      } else {
        const errorMessage = error?.message || "请求失败，请重试";
        toast.error(errorMessage);
        if (setError) setError(errorMessage);
      }

      if (error?.openLogin) {
        setShowLoginModal(true);
      }
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="relative w-full ask-ai-component">
      {/* Selected Element Display */}
      {selectedElement && setSelectedElement && (
        <SelectedHtmlElement
          element={selectedElement}
          isAiWorking={isGenerating}
          onDelete={() => setSelectedElement(null)}
        />
      )}

      {/* Main Input Container - 使用flex布局确保自适应 */}
      <div 
        ref={containerRef}
        className={classNames(
          "relative rounded-2xl border transition-all duration-300 flex flex-col group",
          {
            // Welcome mode styling - 现代化设计
            "bg-white/95 dark:bg-black/70 backdrop-blur-xl border-gray-300/50 dark:border-white/15 hover:border-gray-400/70 dark:hover:border-white/25 focus-within:border-blue-500/80 dark:focus-within:border-blue-400/70 shadow-lg hover:shadow-xl focus-within:shadow-2xl": isWelcomeMode,
            // Chat/Edit mode styling
            "bg-secondary border-border": !isWelcomeMode,
            // Dragging state
            "border-blue-400 bg-blue-500/10 shadow-blue-500/20 shadow-lg": isDragging,
          }
        )}
        style={isWelcomeMode ? {
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1) inset',
        } : undefined}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* 图片预览区域 - 如果有图片则显示 */}
        {uploadedImages.length > 0 && (
          <div className="p-3 border-b border-neutral-700/30">
            <div className="flex items-start gap-3">
              {/* 图片预览 */}
              <div className="flex flex-wrap gap-2">
                {imageUrls.map((url, index) => (
                  <div key={index} className="relative group">
                    <div className="relative w-16 h-16 rounded-lg overflow-hidden border-2 border-neutral-600/50 bg-neutral-800/90">
                      <img
                        src={url}
                        alt={`预览图片 ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        onClick={() => handleRemoveImage(index)}
                        className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity w-4 h-4 flex items-center justify-center text-white bg-red-500 hover:bg-red-600 rounded-full text-xs font-bold"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* 快捷提示词 */}
              <div className="flex flex-wrap gap-2">
                {quickPrompts.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickPrompt(prompt)}
                    className={classNames(
                      "px-2 py-1 text-xs rounded-md transition-all duration-200 border font-medium",
                      {
                        "bg-neutral-700/90 border-neutral-600/50 text-neutral-200 hover:bg-neutral-600/90 hover:text-white": !isWelcomeMode,
                        "bg-white/15 dark:bg-white/15 bg-gray-100/80 border-white/25 dark:border-white/25 border-gray-300/60 text-neutral-100 dark:text-neutral-100 text-gray-700 hover:bg-white/25 dark:hover:bg-white/25 hover:bg-gray-200/80 hover:text-white dark:hover:text-white hover:text-gray-800": isWelcomeMode,
                      }
                    )}
                    disabled={isGenerating}
                  >
                    {prompt}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 输入框区域 - 增加纵向内边距 */}
        <div className={classNames(
          "flex-1 px-3",
          {
            "py-3 sm:py-4": isWelcomeMode,  // 欢迎模式：移动端减少纵向空间
            "py-2": !isWelcomeMode, // 普通模式保持原有间距
          }
        )}>
          <textarea
            ref={textareaRef}
            value={currentInputValue}
            onChange={handleTextareaChange}
            onKeyDown={handleKeyDown}
            onFocus={handleTextareaFocus}
            onClick={handleTextareaClick}
            onPaste={handlePaste}
            placeholder={displayPlaceholder}
            className={classNames(
              "w-full resize-none bg-transparent border-none outline-none transition-all duration-200",
              {
                "text-sm sm:text-base md:text-lg min-h-[1.5rem] max-h-48 text-black dark:text-white placeholder-gray-400 dark:placeholder-gray-500 leading-relaxed": isWelcomeMode,
                "text-sm min-h-[1.25rem] max-h-32 text-foreground placeholder-muted-foreground": !isWelcomeMode,
              }
            )}
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              fontFamily: isWelcomeMode ? '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif' : undefined,
            }}
            disabled={isGenerating}
            rows={1}
            spellCheck={false}
            autoComplete="off"
            autoCorrect="off"
            autoCapitalize="off"
            data-ms-editor="false"
          />
        </div>

        {/* 底部工具栏 - 重新设计布局 */}
        <div className="flex items-center gap-1 px-3 py-1 min-h-[2rem]">
          {/* 左侧模型选择器 */}
          <div className="flex items-center gap-0.5 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <ModelSelector
                selectedModel={isMounted ? selectedModel : "loomrun-1.2ds"}
                onModelChange={setSelectedModel}
                disabled={isGenerating}
                isWelcomeMode={isWelcomeMode}
              />
            </div>
          </div>

          {/* 右侧按钮组 - 图片、MCP、发送按钮紧挨着 */}
          <div className="flex items-center gap-1">
            {/* 图片上传按钮 - 在项目内页面隐藏 */}
            {!isInProject && (
              <button
                onClick={handleImageUpload}
                className={classNames(
                  "px-0.5 py-0.5 rounded flex items-center gap-0.5 transition-all duration-200 flex-shrink-0 min-w-0",
                  {
                    "hover:bg-white/10 dark:hover:bg-white/10 hover:bg-gray-100/50 text-gray-700 dark:text-neutral-300 hover:text-blue-600 dark:hover:text-blue-300": isWelcomeMode,
                    "hover:bg-white/10 text-neutral-400 hover:text-blue-400": !isWelcomeMode,
                  }
                )}
                title="上传图片"
                disabled={isGenerating}
              >
                <ImagePlus className="w-3 h-3 flex-shrink-0" />
                <span className="text-xs font-medium hidden xl:inline">图片</span>
              </button>
            )}

            {/* Figma导入按钮 */}
            <button
              onClick={handleFigmaImport}
              className={classNames(
                "px-0.5 py-0.5 rounded flex items-center gap-0.5 transition-all duration-200 flex-shrink-0 min-w-0",
                {
                  "hover:bg-white/10 dark:hover:bg-white/10 hover:bg-gray-100/50 text-gray-700 dark:text-neutral-300 hover:text-purple-600 dark:hover:text-purple-300": isWelcomeMode,
                  "hover:bg-white/10 text-neutral-400 hover:text-purple-400": !isWelcomeMode,
                }
              )}
              title="从Figma导入"
              disabled={isGenerating}
            >
              <Figma className="w-3 h-3 flex-shrink-0" />
              <span className="text-xs font-medium hidden xl:inline">Figma</span>
            </button>

            {/* 发送按钮 - 现代化设计 */}
            <button
              onClick={handleSubmit}
              disabled={isGenerating || !currentInputValue.trim()}
              className={classNames(
                "w-10 h-6 rounded-lg flex items-center justify-center transition-all duration-300 flex-shrink-0 group/send relative overflow-hidden",
                {
                  "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95": !isGenerating && currentInputValue.trim(),
                  "bg-neutral-600 text-neutral-400 cursor-not-allowed": isGenerating || !currentInputValue.trim(),
                }
              )}
              title={isGenerating ? "生成中..." : "发送"}
              style={!isGenerating && currentInputValue.trim() ? {
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset',
              } : undefined}
            >
              {/* 发光效果 */}
              {!isGenerating && currentInputValue.trim() && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 opacity-0 group-hover/send:opacity-100 transition-opacity duration-300" />
              )}

              {isGenerating ? (
                <FaStopCircle className="w-3 h-3 relative z-10" />
              ) : (
                <ArrowUp className="w-3 h-3 relative z-10 transition-transform duration-200 group-hover/send:translate-y-[-1px]" />
              )}
            </button>
          </div>
        </div>

        {/* Hide scrollbar with CSS - 强力隐藏AI输入框滚动条 */}
        <style jsx>{`
          textarea::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
          
          textarea::-webkit-scrollbar-track {
            display: none !important;
          }
          
          textarea::-webkit-scrollbar-thumb {
            display: none !important;
          }
          
          textarea::-webkit-scrollbar-corner {
            display: none !important;
          }
          
          textarea {
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
          }
          
          /* 🎨 专业的打字机光标效果 */
          textarea::placeholder {
            opacity: 0.7;
            font-style: normal;
            letter-spacing: 0.02em;
            transition: all 0.3s ease;
          }
          
          /* 欢迎模式下的特殊样式 */
          .welcome-textarea::placeholder {
            opacity: 0.85;
            font-weight: 400;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
            animation: subtle-glow 4s ease-in-out infinite alternate;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          }
          
          .welcome-textarea:focus::placeholder {
            opacity: 0.6;
            transform: translateY(-1px);
          }
          
          @keyframes subtle-glow {
            0% {
              text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
              opacity: 0.75;
            }
            50% {
              text-shadow: 0 0 15px rgba(255, 255, 255, 0.2), 0 0 25px rgba(59, 130, 246, 0.1);
              opacity: 0.9;
            }
            100% {
              text-shadow: 0 0 10px rgba(255, 255, 255, 0.15), 0 0 20px rgba(139, 92, 246, 0.1);
              opacity: 0.8;
            }
          }
          
          /* 光标闪烁动画 - 更自然的效果 */
          @keyframes cursor-blink {
            0%, 45% { opacity: 1; }
            50%, 95% { opacity: 0.3; }
            100% { opacity: 1; }
          }
          
          /* 容器静态样式 - 移除悬停效果 */
          .welcome-container {
            /* 保持静态样式，无悬停变化 */
          }
        `}</style>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        className="hidden"
        onChange={handleFileChange}
      />

      {/* Error Display */}
      {error && (
        <div className="mt-2 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-sm text-red-400">
          {error}
        </div>
      )}

      {/* Mode Toggle Button (if needed) */}
      {showModeToggle && onModeToggle && (
        <div className="mt-2 flex justify-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={onModeToggle}
            className="text-neutral-400 hover:text-white"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            切换模式
          </Button>
        </div>
      )}

      {/* Modals */}
      <LoginModal
        open={showLoginModal}
        html=""
        onClose={() => setShowLoginModal(false)}
      />
      
      <ProModal
        open={showProModal}
        html=""
        onClose={() => setShowProModal(false)}
      />

      {/* Figma集成弹窗 - 简化版本 */}
      <FigmaIntegration
        isOpen={isFigmaModalOpen}
        onClose={() => setIsFigmaModalOpen(false)}
      />
    </div>
  );
}

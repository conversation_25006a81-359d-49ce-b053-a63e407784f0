"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Coins, 
  TrendingUp, 
  TrendingDown, 
  History, 
  Gift, 
  Zap, 
  Download, 
  CreditCard,
  Users,
  Calendar,
  ArrowLeft,
  RefreshCw,
  Clock,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { Header } from "@/components/editor/header";

interface PointsTransaction {
  id: number;
  transaction_type: 'earn' | 'spend';
  points_amount: number;
  balance_before: number;
  balance_after: number;
  source_type: string;
  points_type: string;
  description: string;
  displayDescription?: string; // 用户友好的描述
  displayModelName?: string;   // 用户友好的模型名称
  expires_at: string | null;
  created_at: string;
}

interface PointsBalance {
  id: number;
  points_type: 'activity' | 'subscription' | 'recharge';
  points_amount: number;
  expires_at: string | null;
  created_at: string;
}

export default function PointsPage() {
  const { user, loading } = useUser();
  const router = useRouter();
  const [transactions, setTransactions] = useState<PointsTransaction[]>([]);
  const [balances, setBalances] = useState<PointsBalance[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [balancesLoading, setBalancesLoading] = useState(false);

  // 获取积分历史
  const fetchTransactions = async () => {
    setTransactionsLoading(true);
    try {
      const response = await fetch('/api/points/history?limit=50');
      const data = await response.json();
      if (data.success) {
        setTransactions(data.data.history);
      } else {
        toast.error('获取积分历史失败');
      }
    } catch (error) {
      console.error('获取积分历史失败:', error);
      toast.error('获取积分历史失败');
    } finally {
      setTransactionsLoading(false);
    }
  };

  // 获取积分余额详情
  const fetchBalances = async () => {
    setBalancesLoading(true);
    try {
      const response = await fetch('/api/points/balance');
      const data = await response.json();
      if (data.success) {
        setBalances(data.data.balances);
      } else {
        toast.error('获取积分余额失败');
      }
    } catch (error) {
      console.error('获取积分余额失败:', error);
      toast.error('获取积分余额失败');
    } finally {
      setBalancesLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchTransactions();
      fetchBalances();
    }
  }, [user]);

  // 如果用户未登录，重定向到首页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [user, loading, router]);

  const getSourceTypeLabel = (sourceType: string) => {
    const labels: Record<string, string> = {
      registration: '注册奖励',
      ai_request: 'AI请求',
      export: '项目导出',
      invitation: '邀请奖励',
      recharge: '充值',
      subscription: '订阅',
      admin_adjust: '管理员调整',
      refund: '退款'
    };
    return labels[sourceType] || sourceType;
  };

  const getSourceTypeIcon = (sourceType: string) => {
    const icons: Record<string, React.ReactNode> = {
      registration: <Gift className="w-4 h-4" />,
      ai_request: <Zap className="w-4 h-4" />,
      export: <Download className="w-4 h-4" />,
      invitation: <Users className="w-4 h-4" />,
      recharge: <CreditCard className="w-4 h-4" />,
      subscription: <Calendar className="w-4 h-4" />,
      admin_adjust: <AlertCircle className="w-4 h-4" />,
      refund: <RefreshCw className="w-4 h-4" />
    };
    return icons[sourceType] || <Coins className="w-4 h-4" />;
  };

  const getPointsTypeColor = (pointsType: string) => {
    const colors: Record<string, string> = {
      activity: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      subscription: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      recharge: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
    };
    return colors[pointsType] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  const getPointsTypeLabel = (pointsType: string) => {
    const labels: Record<string, string> = {
      activity: '活动积分',
      subscription: '订阅积分',
      recharge: '充值积分'
    };
    return labels[pointsType] || pointsType;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const isExpiringSoon = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    const expireDate = new Date(expiresAt);
    const now = new Date();
    const diffDays = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays > 0;
  };

  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="flex items-center justify-center h-screen">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 使用系统的 Header 组件 */}
      <Header
        onLogoClick={() => router.push('/')}
      >
        {/* 在 Header 右侧添加返回按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2 text-sm"
        >
          <ArrowLeft className="w-4 h-4" />
          返回
        </Button>
      </Header>

      {/* 主内容区域 - 占据剩余空间 */}
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Coins className="w-8 h-8 text-blue-600" />
            我的积分
          </h1>
          <p className="text-muted-foreground mt-1">管理和查看您的积分详情</p>
        </div>

        {/* 积分概览卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 border-blue-200 dark:border-blue-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">当前积分</p>
                  <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">{user.points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Coins className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 border-green-200 dark:border-green-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">累计获得</p>
                  <p className="text-3xl font-bold text-green-700 dark:text-green-300">{user.total_earned_points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-900/50 border-red-200 dark:border-red-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600 dark:text-red-400">累计消费</p>
                  <p className="text-3xl font-bold text-red-700 dark:text-red-300">{user.total_spent_points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                  <TrendingDown className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 详细信息标签页 */}
        <Tabs defaultValue="history" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="w-4 h-4" />
              积分历史
            </TabsTrigger>
            <TabsTrigger value="balance" className="flex items-center gap-2">
              <Coins className="w-4 h-4" />
              积分余额
            </TabsTrigger>
          </TabsList>

          {/* 积分历史标签页 */}
          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="w-5 h-5" />
                  积分变动历史
                </CardTitle>
                <CardDescription>查看您的所有积分获得和消费记录</CardDescription>
              </CardHeader>
              <CardContent>
                {transactionsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
                  </div>
                ) : transactions.length > 0 ? (
                  <div className="space-y-4">
                    {transactions.map((transaction) => (
                      <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-4">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            transaction.transaction_type === 'earn' 
                              ? 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400' 
                              : 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                          }`}>
                            {getSourceTypeIcon(transaction.source_type)}
                          </div>
                          <div>
                            <div className="font-medium">{transaction.displayDescription || transaction.description}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-2">
                              <span>{getSourceTypeLabel(transaction.source_type)}</span>
                              {transaction.points_type && (
                                <Badge className={getPointsTypeColor(transaction.points_type)} variant="secondary">
                                  {getPointsTypeLabel(transaction.points_type)}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-lg font-bold ${
                            transaction.transaction_type === 'earn' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.transaction_type === 'earn' ? '+' : '-'}{transaction.points_amount}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            余额: {transaction.balance_after}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {formatDate(transaction.created_at)}
                          </div>
                          {transaction.expires_at && (
                            <div className={`text-xs flex items-center gap-1 mt-1 ${
                              isExpired(transaction.expires_at) 
                                ? 'text-red-600' 
                                : isExpiringSoon(transaction.expires_at) 
                                  ? 'text-orange-600' 
                                  : 'text-muted-foreground'
                            }`}>
                              <Clock className="w-3 h-3" />
                              {isExpired(transaction.expires_at) 
                                ? '已过期' 
                                : `有效期至: ${new Date(transaction.expires_at).toLocaleDateString()}`
                              }
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <History className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>暂无积分变动记录</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 积分余额标签页 */}
          <TabsContent value="balance">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Coins className="w-5 h-5" />
                  积分余额详情
                </CardTitle>
                <CardDescription>查看不同类型积分的余额和有效期</CardDescription>
              </CardHeader>
              <CardContent>
                {balancesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
                  </div>
                ) : balances.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {balances.map((balance) => (
                      <Card key={balance.id} className="relative">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <Badge className={getPointsTypeColor(balance.points_type)} variant="secondary">
                              {getPointsTypeLabel(balance.points_type)}
                            </Badge>
                            {balance.expires_at && isExpiringSoon(balance.expires_at) && (
                              <Badge variant="destructive" className="text-xs">
                                即将过期
                              </Badge>
                            )}
                          </div>
                          <div className="text-2xl font-bold mb-2">{balance.points_amount}</div>
                          <div className="text-sm text-muted-foreground">
                            {balance.expires_at ? (
                              <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {isExpired(balance.expires_at) 
                                  ? '已过期' 
                                  : `有效期至: ${new Date(balance.expires_at).toLocaleDateString()}`
                                }
                              </div>
                            ) : (
                              <div className="flex items-center gap-1">
                                <Coins className="w-3 h-3" />
                                永久有效
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Coins className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>暂无积分余额</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </div>
  );
}

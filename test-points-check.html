<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分检查测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 积分不足功能测试</h1>
        <p>此页面用于测试积分不足时的友好提示功能</p>

        <div class="test-section">
            <h3>1. 积分余额查询</h3>
            <button onclick="testPointsBalance()">查询当前积分余额</button>
            <div id="balance-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 积分预检查测试</h3>
            <input type="text" id="test-prompt" placeholder="输入测试提示词" value="创建一个简单的网站">
            <br>
            <button onclick="testPreCheck()">测试积分预检查</button>
            <button onclick="testPreCheckPUT()">测试PUT请求预检查</button>
            <div id="precheck-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 完整AI请求测试</h3>
            <input type="text" id="full-prompt" placeholder="输入完整测试提示词" value="5858写在页面中间即可">
            <br>
            <button onclick="testFullRequest()">测试完整POST请求</button>
            <button onclick="testFullRequestPUT()">测试完整PUT请求</button>
            <div id="full-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 模拟积分不足</h3>
            <p style="color: #666;">注意：这些测试可能会消耗实际积分</p>
            <button class="danger" onclick="simulateInsufficientPoints()">模拟积分不足场景</button>
            <div id="simulate-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 1. 测试积分余额查询
        async function testPointsBalance() {
            try {
                showResult('balance-result', '正在查询积分余额...', 'info');
                
                const response = await fetch('/api/points/balance');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const result = `✅ 积分余额查询成功
总积分: ${data.data.totalPoints}
详细余额: ${JSON.stringify(data.data.summary, null, 2)}`;
                    showResult('balance-result', result, 'success');
                } else {
                    showResult('balance-result', `❌ 查询失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('balance-result', `❌ 查询出错: ${error.message}`, 'error');
            }
        }

        // 2. 测试积分预检查
        async function testPreCheck() {
            try {
                const prompt = document.getElementById('test-prompt').value;
                showResult('precheck-result', '正在进行积分预检查...', 'info');
                
                const response = await fetch('/api/ask-ai', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: [],
                        preCheckOnly: true
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 402) {
                    const result = `⚠️ 积分不足检测正常
状态码: ${response.status}
需要积分: ${data.pointsRequired}
当前积分: ${data.currentPoints}
错误信息: ${data.message}`;
                    showResult('precheck-result', result, 'error');
                } else if (response.ok) {
                    const result = `✅ 积分检查通过
积分消耗: ${data.pointsCost}
当前积分: ${data.currentPoints}
消息: ${data.message}`;
                    showResult('precheck-result', result, 'success');
                } else {
                    showResult('precheck-result', `❌ 预检查失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('precheck-result', `❌ 预检查出错: ${error.message}`, 'error');
            }
        }

        // 3. 测试PUT请求预检查
        async function testPreCheckPUT() {
            try {
                const prompt = document.getElementById('test-prompt').value;
                showResult('precheck-result', '正在进行PUT请求积分预检查...', 'info');
                
                const response = await fetch('/api/ask-ai', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        html: '<div>测试HTML</div>',
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: []
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 402) {
                    const result = `⚠️ PUT请求积分不足检测正常
状态码: ${response.status}
需要积分: ${data.pointsRequired}
当前积分: ${data.currentPoints}
错误信息: ${data.message}`;
                    showResult('precheck-result', result, 'error');
                } else if (response.ok) {
                    showResult('precheck-result', `✅ PUT请求积分检查通过`, 'success');
                } else {
                    showResult('precheck-result', `❌ PUT请求失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('precheck-result', `❌ PUT请求出错: ${error.message}`, 'error');
            }
        }

        // 4. 测试完整POST请求
        async function testFullRequest() {
            try {
                const prompt = document.getElementById('full-prompt').value;
                showResult('full-result', '正在发送完整POST请求...', 'info');
                
                const response = await fetch('/api/ask-ai', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: []
                    })
                });
                
                if (response.status === 402) {
                    const data = await response.json();
                    const result = `⚠️ 完整请求积分不足
状态码: ${response.status}
需要积分: ${data.pointsRequired}
当前积分: ${data.currentPoints}
错误信息: ${data.message}`;
                    showResult('full-result', result, 'error');
                } else if (response.ok) {
                    showResult('full-result', `✅ 完整请求成功，开始流式响应`, 'success');
                } else {
                    const data = await response.json();
                    showResult('full-result', `❌ 完整请求失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('full-result', `❌ 完整请求出错: ${error.message}`, 'error');
            }
        }

        // 5. 测试完整PUT请求
        async function testFullRequestPUT() {
            try {
                const prompt = document.getElementById('full-prompt').value;
                showResult('full-result', '正在发送完整PUT请求...', 'info');
                
                const response = await fetch('/api/ask-ai', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        html: '<div>测试HTML内容</div>',
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: []
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 402) {
                    const result = `⚠️ PUT请求积分不足
状态码: ${response.status}
需要积分: ${data.pointsRequired}
当前积分: ${data.currentPoints}
错误信息: ${data.message}`;
                    showResult('full-result', result, 'error');
                } else if (response.ok) {
                    showResult('full-result', `✅ PUT请求成功: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('full-result', `❌ PUT请求失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('full-result', `❌ PUT请求出错: ${error.message}`, 'error');
            }
        }

        // 6. 模拟积分不足场景
        async function simulateInsufficientPoints() {
            showResult('simulate-result', `📋 积分不足功能测试总结:

1. ✅ 后端积分检查: 在AI请求前进行积分验证
2. ✅ 402状态码: 积分不足时返回Payment Required
3. ✅ 详细信息: 包含需要积分数和当前余额
4. ✅ 友好提示: 显示具体的积分信息和充值链接
5. ✅ 消息回退: 积分不足时消息不发送到聊天区
6. ✅ 状态一致性: 确保UI状态的正确性

测试建议:
- 确保用户积分为0或不足
- 尝试发送消息到AI
- 观察是否显示友好的积分不足提示
- 确认消息没有被添加到聊天历史
- 验证消息是否回退到输入框`, 'info');
        }

        // 页面加载时自动查询积分余额
        window.addEventListener('load', function() {
            testPointsBalance();
        });
    </script>
</body>
</html>

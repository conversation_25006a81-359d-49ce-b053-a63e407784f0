INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
('new_user_points_enabled', '1', 'boolean', '新用户注册送积分功能开关', 'points'),
('new_user_points_amount', '100', 'number', '新用户注册送积分数量', 'points'),
('recharge_service_enabled', '0', 'boolean', '充值服务功能开关', 'payment'),
('subscription_service_enabled', '0', 'boolean', '订阅服务功能开关', 'payment');

INSERT INTO ai_models (model_key, model_name, points_per_request, description, display_order) VALUES
('deepseek-chat', 'DeepSeek Chat', 2, 'DeepSeek基础对话模型', 1),
('deepseek-coder', 'DeepSeek Coder', 3, 'DeepSeek代码生成模型', 2),
('doubao-lite-4k', 'Doubao Lite 4K', 1, '豆包轻量级模型', 3),
('doubao-pro-4k', 'Doubao Pro 4K', 3, '豆包专业级模型', 4),
('doubao-pro-32k', 'Doubao Pro 32K', 5, '豆包长文本模型', 5),
('doubao-pro-128k', 'Doubao Pro 128K', 8, '豆包超长文本模型', 6);

INSERT INTO export_types (export_key, export_name, points_cost, description, display_order) VALUES
('figma', '导出到Figma', 0, '导出项目到Figma设计工具', 1),
('image', '导出为图片', 0, '导出项目为图片格式', 2),
('html', '导出为HTML', 0, '导出项目为HTML文件', 3),
('pptx', '导出为PPTX', 0, '导出项目为PowerPoint演示文稿', 4);

INSERT INTO points_packages (package_key, package_name, points_amount, original_price, discount_price, bonus_points, description, display_order) VALUES
('basic_100', '基础包', 100, 10.00, 10.00, 0, '100积分充值包', 1),
('standard_500', '标准包', 500, 50.00, 45.00, 50, '500积分充值包，赠送50积分', 2),
('premium_1000', '高级包', 1000, 100.00, 85.00, 150, '1000积分充值包，赠送150积分', 3),
('ultimate_2000', '旗舰包', 2000, 200.00, 160.00, 400, '2000积分充值包，赠送400积分', 4);

INSERT INTO subscription_plans (plan_key, plan_name, duration_months, original_price, discount_price, points_included, features, display_order) VALUES
('basic_monthly', '基础月度会员', 1, 29.00, 29.00, 500, '{"ai_requests": "unlimited", "exports": "unlimited", "priority_support": false}', 1),
('pro_monthly', '专业月度会员', 1, 59.00, 59.00, 1200, '{"ai_requests": "unlimited", "exports": "unlimited", "priority_support": true, "advanced_models": true}', 2),
('basic_yearly', '基础年度会员', 12, 348.00, 290.00, 6000, '{"ai_requests": "unlimited", "exports": "unlimited", "priority_support": false}', 3),
('pro_yearly', '专业年度会员', 12, 708.00, 590.00, 15000, '{"ai_requests": "unlimited", "exports": "unlimited", "priority_support": true, "advanced_models": true}', 4);

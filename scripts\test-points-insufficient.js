/**
 * 积分不足功能测试脚本
 * 
 * 使用方法：
 * 1. 在浏览器控制台中运行此脚本
 * 2. 或者将此脚本内容复制到浏览器控制台执行
 */

// 测试积分不足功能
async function testPointsInsufficient() {
  console.log('🧪 开始测试积分不足功能...');
  
  try {
    // 模拟发送AI请求
    const response = await fetch('/api/ask-ai', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: '创建一个简单的网站',
        provider: 'deepseek-official',
        model: 'deepseek-chat',
        images: []
      }),
    });

    console.log('📊 响应状态:', response.status);
    
    if (response.status === 402) {
      const errorData = await response.json();
      console.log('✅ 积分不足检测正常工作!');
      console.log('📋 错误详情:', {
        message: errorData.message,
        pointsRequired: errorData.pointsRequired,
        currentPoints: errorData.currentPoints,
        openProModal: errorData.openProModal
      });
      
      // 测试友好提示格式
      const expectedMessage = `积分不足！需要 ${errorData.pointsRequired} 积分，当前余额 ${errorData.currentPoints} 积分。请充值后继续使用。`;
      console.log('💬 友好提示消息:', expectedMessage);
      
      return {
        success: true,
        message: '积分不足功能正常工作',
        data: errorData
      };
    } else if (response.ok) {
      console.log('ℹ️ 用户积分充足，AI请求正常处理');
      return {
        success: true,
        message: '用户积分充足，无需测试积分不足功能'
      };
    } else {
      console.error('❌ 意外的响应状态:', response.status);
      const errorText = await response.text();
      console.error('错误内容:', errorText);
      return {
        success: false,
        message: '意外的响应状态',
        status: response.status
      };
    }
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return {
      success: false,
      message: '测试过程中发生错误',
      error: error.message
    };
  }
}

// 测试PUT请求的积分不足功能
async function testPointsInsufficientPUT() {
  console.log('🧪 开始测试PUT请求积分不足功能...');
  
  try {
    const response = await fetch('/api/ask-ai', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: '修改网站颜色为蓝色',
        html: '<div>测试HTML</div>',
        provider: 'deepseek-official',
        model: 'deepseek-chat',
        images: []
      }),
    });

    console.log('📊 PUT响应状态:', response.status);
    
    if (response.status === 402) {
      const errorData = await response.json();
      console.log('✅ PUT请求积分不足检测正常工作!');
      console.log('📋 错误详情:', errorData);
      return {
        success: true,
        message: 'PUT请求积分不足功能正常工作',
        data: errorData
      };
    } else if (response.ok) {
      console.log('ℹ️ 用户积分充足，PUT请求正常处理');
      return {
        success: true,
        message: '用户积分充足，无需测试积分不足功能'
      };
    } else {
      console.error('❌ PUT请求意外的响应状态:', response.status);
      return {
        success: false,
        message: 'PUT请求意外的响应状态',
        status: response.status
      };
    }
  } catch (error) {
    console.error('❌ PUT请求测试过程中发生错误:', error);
    return {
      success: false,
      message: 'PUT请求测试过程中发生错误',
      error: error.message
    };
  }
}

// 测试用户积分余额查询
async function testUserPointsBalance() {
  console.log('🧪 测试用户积分余额查询...');
  
  try {
    const response = await fetch('/api/points/balance');
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ 积分余额查询成功!');
      console.log('📊 积分详情:', {
        totalPoints: data.data.totalPoints,
        balances: data.data.balances,
        summary: data.data.summary
      });
      return {
        success: true,
        message: '积分余额查询成功',
        data: data.data
      };
    } else {
      console.error('❌ 积分余额查询失败:', data);
      return {
        success: false,
        message: '积分余额查询失败',
        error: data
      };
    }
  } catch (error) {
    console.error('❌ 积分余额查询过程中发生错误:', error);
    return {
      success: false,
      message: '积分余额查询过程中发生错误',
      error: error.message
    };
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行所有积分相关测试...');
  console.log('='.repeat(50));
  
  // 1. 测试积分余额查询
  console.log('\n1️⃣ 测试积分余额查询');
  const balanceResult = await testUserPointsBalance();
  console.log('结果:', balanceResult);
  
  // 2. 测试POST请求积分不足
  console.log('\n2️⃣ 测试POST请求积分不足');
  const postResult = await testPointsInsufficient();
  console.log('结果:', postResult);
  
  // 3. 测试PUT请求积分不足
  console.log('\n3️⃣ 测试PUT请求积分不足');
  const putResult = await testPointsInsufficientPUT();
  console.log('结果:', putResult);
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 所有测试完成!');
  
  return {
    balanceTest: balanceResult,
    postTest: postResult,
    putTest: putResult
  };
}

// 导出测试函数（如果在Node.js环境中使用）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPointsInsufficient,
    testPointsInsufficientPUT,
    testUserPointsBalance,
    runAllTests
  };
}

// 在浏览器控制台中可以直接调用的函数
window.testPointsFeature = runAllTests;

console.log('📝 积分不足功能测试脚本已加载!');
console.log('💡 使用方法:');
console.log('   - 运行所有测试: runAllTests()');
console.log('   - 测试POST请求: testPointsInsufficient()');
console.log('   - 测试PUT请求: testPointsInsufficientPUT()');
console.log('   - 测试积分余额: testUserPointsBalance()');
console.log('   - 快捷方式: testPointsFeature()');

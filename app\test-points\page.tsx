"use client";

import { useState } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Coins, Gift, History, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface PointsHistory {
  id: number;
  transaction_type: 'earn' | 'spend';
  points_amount: number;
  balance_before: number;
  balance_after: number;
  source_type: string;
  points_type: string;
  description: string;
  expires_at: string;
  created_at: string;
}

export default function TestPointsPage() {
  const { user, refreshUser } = useUser();
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState<PointsHistory[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  // 测试发放新用户积分
  const handleGrantPoints = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test/grant-points', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        await refreshUser(); // 刷新用户信息
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('发放积分失败:', error);
      toast.error('发放积分失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取积分历史
  const handleGetHistory = async () => {
    setHistoryLoading(true);
    try {
      const response = await fetch('/api/points/history?limit=10');
      const data = await response.json();

      if (data.success) {
        setHistory(data.data.history);
        toast.success('积分历史获取成功');
      } else {
        toast.error('获取积分历史失败');
      }
    } catch (error) {
      console.error('获取积分历史失败:', error);
      toast.error('获取积分历史失败');
    } finally {
      setHistoryLoading(false);
    }
  };

  const getSourceTypeLabel = (sourceType: string) => {
    const labels: Record<string, string> = {
      registration: '注册奖励',
      ai_request: 'AI请求',
      export: '项目导出',
      invitation: '邀请奖励',
      recharge: '充值',
      subscription: '订阅',
      admin_adjust: '管理员调整',
      refund: '退款'
    };
    return labels[sourceType] || sourceType;
  };

  const getPointsTypeColor = (pointsType: string) => {
    const colors: Record<string, string> = {
      activity: 'bg-green-100 text-green-800',
      subscription: 'bg-blue-100 text-blue-800',
      recharge: 'bg-purple-100 text-purple-800'
    };
    return colors[pointsType] || 'bg-gray-100 text-gray-800';
  };

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">请先登录</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <Coins className="w-6 h-6" />
        <h1 className="text-2xl font-bold">积分系统测试</h1>
      </div>

      {/* 用户积分信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="w-5 h-5" />
            我的积分
          </CardTitle>
          <CardDescription>当前积分余额和统计信息</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{user.points || 0}</div>
              <div className="text-sm text-muted-foreground">当前积分</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{user.total_earned_points || 0}</div>
              <div className="text-sm text-muted-foreground">累计获得</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{user.total_spent_points || 0}</div>
              <div className="text-sm text-muted-foreground">累计消费</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试功能 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="w-5 h-5" />
            测试功能
          </CardTitle>
          <CardDescription>测试积分系统的各项功能</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={handleGrantPoints} 
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <Gift className="w-4 h-4" />
              发放新用户积分
            </Button>
            
            <Button 
              onClick={handleGetHistory} 
              disabled={historyLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {historyLoading && <RefreshCw className="w-4 h-4 animate-spin" />}
              <History className="w-4 h-4" />
              获取积分历史
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 积分历史 */}
      {history.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5" />
              积分历史
            </CardTitle>
            <CardDescription>最近的积分变动记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {history.map((record) => (
                <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant={record.transaction_type === 'earn' ? 'default' : 'destructive'}>
                      {record.transaction_type === 'earn' ? '+' : '-'}{record.points_amount}
                    </Badge>
                    <div>
                      <div className="font-medium">{record.displayDescription || record.description}</div>
                      <div className="text-sm text-muted-foreground">
                        {getSourceTypeLabel(record.source_type)}
                        {record.points_type && (
                          <Badge className={`ml-2 ${getPointsTypeColor(record.points_type)}`} variant="secondary">
                            {record.points_type}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">余额: {record.balance_after}</div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(record.created_at).toLocaleString()}
                    </div>
                    {record.expires_at && (
                      <div className="text-xs text-orange-600">
                        有效期至: {new Date(record.expires_at).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

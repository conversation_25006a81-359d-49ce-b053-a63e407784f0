const mysql = require('mysql2/promise');

async function checkData() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'loomrun',
      charset: 'utf8mb4'
    });

    console.log('🔍 检查积分交易记录的实际数据...');
    
    const [records] = await connection.execute(`
      SELECT id, description, metadata, source_type, created_at 
      FROM points_transactions 
      WHERE source_type = 'ai_request' 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log('📋 最近的AI请求积分记录:');
    records.forEach((record, index) => {
      console.log(`\n记录 ${index + 1}:`);
      console.log(`  ID: ${record.id}`);
      console.log(`  描述: ${record.description}`);
      console.log(`  元数据: ${record.metadata}`);
      console.log(`  来源类型: ${record.source_type}`);
      console.log(`  创建时间: ${record.created_at}`);
      
      if (record.metadata) {
        try {
          const metadata = JSON.parse(record.metadata);
          console.log(`  解析后的元数据:`, metadata);
        } catch (e) {
          console.log(`  元数据解析失败: ${e.message}`);
        }
      }
    });
    
    // 检查所有积分记录
    console.log('\n🔍 检查所有最近的积分记录...');
    const [allRecords] = await connection.execute(`
      SELECT id, description, metadata, source_type, created_at 
      FROM points_transactions 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log('📋 所有最近的积分记录:');
    allRecords.forEach((record, index) => {
      console.log(`\n记录 ${index + 1}:`);
      console.log(`  ID: ${record.id}`);
      console.log(`  描述: ${record.description}`);
      console.log(`  来源类型: ${record.source_type}`);
      
      if (record.metadata) {
        try {
          const metadata = JSON.parse(record.metadata);
          console.log(`  元数据中的model_key: ${metadata.model_key || '无'}`);
        } catch (e) {
          console.log(`  元数据解析失败`);
        }
      }
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) await connection.end();
  }
}

checkData();

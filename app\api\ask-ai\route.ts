/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";

import { MODELS, PROVIDERS } from "@/lib/providers";
import { DeepSeekClient } from "@/lib/deepseek-client";
import { DoubaoClient } from "@/lib/doubao-client";
import {
  DIVIDER,
  FOLLOW_UP_SYSTEM_PROMPT,
  REPLACE_END,
  SEARCH_START,
  getLanguagePrompt,
  getAppTypePrompt,
  PUT_USER_FALLBACK,
  buildContextPrompt,
} from "@/lib/prompts";
import {
  DOUBAO_DIVIDER,
  DOUBAO_FOLLOW_UP_SYSTEM_PROMPT,
  DOUBAO_REPLACE_END,
  DOUBAO_SEARCH_START,
  getDoubaoAppTypePrompt,
  buildDoubaoContextPrompt,
  buildDoubaoImagePrompt,
  DOUBAO_PUT_USER_FALLBACK,
} from "@/lib/doubao-prompts";
import { getUserByToken } from "@/lib/auth-service";
import {
  preCheckAndReservePoints,
  chargePointsForAIRequest,
  detectAIError
} from "@/lib/ai-points-service";


// 🔧 防重复生成的缓存 - 基于提示和图片内容
const generationCache = new Map<string, { inProgress: boolean; timestamp: number }>();
const GENERATION_TTL = 60000; // 60秒内相同请求视为重复

// 清理过期缓存
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of generationCache.entries()) {
    if (now - value.timestamp > GENERATION_TTL) {
      generationCache.delete(key);
    }
  }
}, 120000); // 每2分钟清理一次

// 🔧 SEARCH/REPLACE块解析和应用函数
function applySearchReplaceBlocks(originalHtml: string, response: string): string | null {
  try {
    console.log('🔍 解析SEARCH/REPLACE块...');
    let modifiedHtml = originalHtml;
    
    // 正则表达式匹配所有SEARCH/REPLACE块
    const searchReplaceRegex = /<<<<<<< SEARCH\s*([\s\S]*?)\s*=======\s*([\s\S]*?)\s*>>>>>>> REPLACE/g;
    let match;
    let replacementCount = 0;
    
    while ((match = searchReplaceRegex.exec(response)) !== null) {
      const searchText = match[1].trim();
      const replaceText = match[2].trim();
      
      console.log(`🔍 SEARCH块 ${replacementCount + 1}:`, searchText.substring(0, 100) + '...');
      console.log(`🔄 REPLACE块 ${replacementCount + 1}:`, replaceText.substring(0, 100) + '...');
      
      // 在HTML中查找并替换
      if (modifiedHtml.includes(searchText)) {
        modifiedHtml = modifiedHtml.replace(searchText, replaceText);
        replacementCount++;
        console.log(`✅ 替换成功 ${replacementCount}`);
      } else {
        console.warn(`⚠️ 未找到搜索文本:`, searchText.substring(0, 50) + '...');
      }
    }
    
    console.log(`🎯 总共应用了 ${replacementCount} 个替换`);
    return replacementCount > 0 ? modifiedHtml : null;
  } catch (error) {
    console.error('❌ SEARCH/REPLACE解析错误:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  const authHeaders = await headers();

  // 🔐 检查用户认证状态 - 必须登录才能使用
  const cookieHeader = authHeaders.get('cookie');
  const token = cookieHeader?.split(';')
    .find(c => c.trim().startsWith('loomrun_token='))
    ?.split('=')[1];

  if (!token) {
    console.log('⚠️ ask-ai: 用户未登录，拒绝请求');
    return NextResponse.json(
      {
        ok: false,
        openLogin: true,
        message: "请登录后使用AI生成功能"
      },
      { status: 401 }
    );
  }

  // 获取用户信息
  const user = await getUserByToken(token);
  if (!user) {
    console.log('⚠️ ask-ai: 无效的用户token');
    return NextResponse.json(
      {
        ok: false,
        openLogin: true,
        message: "用户认证失败，请重新登录"
      },
      { status: 401 }
    );
  }

  const body = await request.json();
  const { prompt, provider, model, redesignMarkdown, html, images } = body;

  // 添加详细的请求参数日志
  console.log('🔍 POST /api/ask-ai 请求参数:', {
    hasPrompt: !!prompt,
    hasRedesignMarkdown: !!redesignMarkdown,
    provider: provider,
    model: model,
    hasHtml: !!html,
    hasImages: !!images,
    imagesCount: images?.length || 0,
    promptLength: prompt?.length || 0,
    redesignMarkdownLength: redesignMarkdown?.length || 0
  });

  if (!model || (!prompt && !redesignMarkdown)) {
    console.error('❌ POST请求参数验证失败: 缺少必要字段', {
      hasModel: !!model,
      hasPrompt: !!prompt,
      hasRedesignMarkdown: !!redesignMarkdown
    });
    return NextResponse.json(
      { ok: false, error: "Missing required fields" },
      { status: 400 }
    );
  }

  const selectedModel = MODELS.find(
    (m) => m.value === model || m.label === model
  );
  if (!selectedModel) {
    console.error('❌ POST请求模型验证失败:', {
      requestedModel: model,
      availableModels: MODELS.map(m => ({ value: m.value, label: m.label }))
    });
    return NextResponse.json(
      { ok: false, error: "Invalid model selected" },
      { status: 400 }
    );
  }

  console.log('✅ 模型验证成功:', {
    selectedModel: selectedModel.value,
    providers: selectedModel.providers,
    requestedProvider: provider
  });

  if (!selectedModel.providers.includes(provider) && provider !== "auto") {
    console.error('❌ POST请求提供商验证失败:', {
      selectedModel: selectedModel.value,
      supportedProviders: selectedModel.providers,
      requestedProvider: provider
    });
    return NextResponse.json(
      {
        ok: false,
        error: `The selected model does not support the ${provider} provider.`,
        openSelectProvider: true,
      },
      { status: 400 }
    );
  }

  // 根据模型选择API密钥和客户端
  const isDoubaoModel = selectedModel.value.includes('doubao');
  const apiKey = isDoubaoModel ? process.env.DOUBAO_API_KEY : process.env.DEEPSEEK_API_KEY;
  const keyName = isDoubaoModel ? 'DOUBAO_API_KEY' : 'DEEPSEEK_API_KEY';
  
  if (!apiKey) {
    return NextResponse.json(
      {
        ok: false,
        message: `${keyName} is required. Please add ${keyName} to your environment variables.`,
      },
      { status: 500 }
    );
  }

  // 🔍 积分检查 - 在AI请求前检查用户积分是否足够
  const pointsCheck = await preCheckAndReservePoints(user.id, selectedModel.value);
  if (!pointsCheck.canProceed) {
    console.log('⚠️ 用户积分不足:', {
      userId: user.id,
      modelKey: selectedModel.value,
      pointsCost: pointsCheck.pointsCost,
      currentPoints: pointsCheck.currentPoints,
      errorMessage: pointsCheck.errorMessage
    });

    return NextResponse.json(
      {
        ok: false,
        openProModal: true,
        message: pointsCheck.errorMessage || "积分不足，请充值后使用",
        pointsRequired: pointsCheck.pointsCost,
        currentPoints: pointsCheck.currentPoints
      },
      { status: 402 } // Payment Required
    );
  }

  console.log('✅ 积分检查通过:', {
    userId: user.id,
    modelKey: selectedModel.value,
    pointsCost: pointsCheck.pointsCost,
    currentPoints: pointsCheck.currentPoints
  });

  try {
    // 🔧 防重复生成检查
    const cacheKey = `${prompt || redesignMarkdown}:${JSON.stringify(images || [])}`.substring(0, 200);
    const cached = generationCache.get(cacheKey);
    
    if (cached && cached.inProgress && (Date.now() - cached.timestamp) < GENERATION_TTL) {
      console.log('⚠️ Duplicate generation request detected, rejecting:', {
        cacheKey: cacheKey.substring(0, 50) + '...',
        timeSinceStart: Date.now() - cached.timestamp
      });
      
      return NextResponse.json(
        { ok: false, error: "请等待当前生成完成后再试" },
        { status: 429 }
      );
    }
    
    // 🔧 标记生成开始
    generationCache.set(cacheKey, {
      inProgress: true,
      timestamp: Date.now()
    });

    // Create a stream response
    const encoder = new TextEncoder();
    const stream = new TransformStream();
    const writer = stream.writable.getWriter();

    // Start the response
    const response = new NextResponse(stream.readable, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });

    (async () => {
      let completeResponse = "";
      let isTimedOut = false;
      const TIMEOUT_MS = 300000; // 300秒超时，给长内容生成足够时间
      
      try {
        // 根据模型类型创建不同的客户端
        const userInput = redesignMarkdown || prompt;
        let systemPrompt: string;
        let maxAvailableTokens: number;
        let actualMaxTokens: number;
        let chatCompletion: any;

        if (isDoubaoModel) {
          // 豆包模型处理
          const doubaoClient = new DoubaoClient(apiKey);
          
          // 使用豆包专用提示词
          systemPrompt = getDoubaoAppTypePrompt(userInput);
          
          // 使用providers配置中的token限制，但不能超过豆包API的16384限制
          const providerConfig = PROVIDERS["doubao-official"];
          maxAvailableTokens = Math.min(providerConfig.max_tokens, 16384); // 豆包API最大16384
          const estimatedInputTokens = Math.ceil((systemPrompt.length + userInput.length) * 0.3);
          const availableTokens = Math.max(maxAvailableTokens * 0.7, maxAvailableTokens - estimatedInputTokens);
          actualMaxTokens = Math.min(maxAvailableTokens, availableTokens, 16384); // 确保不超过16384
          
          console.log(`📊 豆包Token估算: 输入约${estimatedInputTokens}tokens, 可用生成${availableTokens}tokens, 最大${maxAvailableTokens}tokens`);
          
          // 构建消息，支持图片
          const messages: any[] = [
            {
              role: "system",
              content: systemPrompt
            }
          ];

          // 如果有图片，构建包含图片的用户消息
          if (images && images.length > 0) {
            const imagePrompt = buildDoubaoImagePrompt(userInput, true);
            const content: any[] = [
              { type: 'text', text: imagePrompt }
            ];
            
            // 添加图片
            for (const imageUrl of images) {
              content.push({
                type: 'image_url',
                image_url: { url: imageUrl }
              });
            }
            
            messages.push({
              role: "user",
              content
            });
          } else {
            messages.push({
              role: "user",
              content: buildDoubaoImagePrompt(userInput, false)
            });
          }

          chatCompletion = doubaoClient.chatCompletionStream({
            model: selectedModel.value,
            messages,
            max_tokens: actualMaxTokens,
          });
        } else {
          // DeepSeek模型处理
          const deepSeekClient = new DeepSeekClient(apiKey);
          
          // 使用原有的DeepSeek提示词
          systemPrompt = getAppTypePrompt(userInput) + getLanguagePrompt();
          
          // DeepSeek API限制是8192 tokens
          maxAvailableTokens = 8192;
          const estimatedInputTokens = Math.ceil((systemPrompt.length + userInput.length) * 0.3);
          const availableTokens = Math.max(7000, maxAvailableTokens - estimatedInputTokens);
          actualMaxTokens = Math.min(8192, availableTokens);
          
          console.log(`📊 DeepSeek Token估算: 输入约${estimatedInputTokens}tokens, 可用生成${availableTokens}tokens`);
          
          chatCompletion = deepSeekClient.chatCompletionStream({
            model: selectedModel.value,
            messages: [
              {
                role: "system",
                content: systemPrompt
              },
              {
                role: "user",
                content: userInput
              }
            ],
            max_tokens: actualMaxTokens,
          });
        }

        // 设置总体超时
        const timeoutId = setTimeout(async () => {
          console.warn('Stream timeout reached, forcing completion');
          isTimedOut = true;
          
          // 如果没有HTML结束标签，尝试补全
          if (!completeResponse.includes("</html>")) {
            console.log('🔧 补全未完成的HTML...');
            if (!completeResponse.includes("</body>")) {
              const bodyEnd = "\n</body>";
              completeResponse += bodyEnd;
              await writer.write(encoder.encode(bodyEnd));
            }
            const htmlEnd = "\n</html>";
            completeResponse += htmlEnd;
            await writer.write(encoder.encode(htmlEnd));
          }
          
          // 强制结束流
          try {
            await writer.close();
          } catch (error) {
            console.warn('Error closing writer on timeout:', error);
          }
        }, TIMEOUT_MS);

        let chunkCount = 0;
        for await (const chunk of chatCompletion) {
          chunkCount++;
          
          // 检查是否已超时
          if (isTimedOut) {
            console.log('⏰ 检测到超时，退出生成循环');
            break;
          }
          
          const content = chunk.choices[0]?.delta?.content;
          const finishReason = chunk.choices[0]?.finish_reason;
          
          // 每100个chunk记录一次进度，包含行数估算
          if (chunkCount % 100 === 0) {
            const estimatedLines = Math.ceil(completeResponse.length / 60); // 估算行数（平均每行60字符）
            console.log(`📊 生成进度: ${chunkCount} chunks, ${completeResponse.length} 字符, 约${estimatedLines}行`);
          }
          
          if (content) {
            if (!selectedModel?.isThinker) {
              // 🔧 实时清理Markdown标记和多余文本
              let cleanContent = content;
              
              // 🔧 增强的开头清理：移除各种可能的前缀
              if (completeResponse.length === 0) {
                // 移除开头的 ```html
                if (content.trim().startsWith('```html')) {
                  console.log('🧹 跳过开头的```html标记');
                  cleanContent = content.replace(/^```html\s*\n?/i, '');
                }
                // 🔧 新增：移除开头单独的"html"文本
                else if (content.trim().toLowerCase() === 'html') {
                  console.log('🧹 跳过开头的"html"文本');
                  cleanContent = '';
                }
                // 🔧 新增：移除"html\n<!DOCTYPE"这种模式
                else if (content.toLowerCase().includes('html\n<!doctype')) {
                  console.log('🧹 清理开头的"html"前缀');
                  cleanContent = content.replace(/^html\s*\n?/i, '');
                }
                // 🔧 新增：移除任何在<!DOCTYPE之前的文本
                else if (content.includes('<!DOCTYPE') && !content.startsWith('<!DOCTYPE')) {
                  console.log('🧹 清理DOCTYPE之前的多余文本');
                  const doctypeIndex = content.indexOf('<!DOCTYPE');
                  cleanContent = content.substring(doctypeIndex);
                }
              }
              
              // 如果包含结尾的 ```，移除它
              if (cleanContent.includes('```')) {
                console.log('🧹 移除结尾的```标记');
                cleanContent = cleanContent.replace(/\n?\s*```.*$/i, '');
              }
              
              await writer.write(encoder.encode(cleanContent));
              completeResponse += cleanContent;

              // 实时检测HTML结束标签，立即停止生成
              if (completeResponse.includes("</html>")) {
                const estimatedLines = Math.ceil(completeResponse.length / 60);
                console.log(`🛑 实时检测到</html>，立即停止: ${estimatedLines}行, ${completeResponse.length}字符`);
                break;
              }

              // 动态计算字符限制（基于实际可用tokens，更宽松的限制）
              const dynamicCharLimit = Math.max(50000, actualMaxTokens * 5.5); // 每token约5.5字符，允许更长生成
              if (completeResponse.length > dynamicCharLimit && !completeResponse.includes("</html>")) {
                console.log('🚨 接近token限制，开始补全HTML结构...');
                // 强制补全HTML结构
                if (!completeResponse.includes("</body>")) {
                  const bodyEnd = "\n</body>";
                  completeResponse += bodyEnd;
                  await writer.write(encoder.encode(bodyEnd));
                }
                const htmlEnd = "\n</html>";
                completeResponse += htmlEnd;
                await writer.write(encoder.encode(htmlEnd));
                break;
              }

              // 检查是否遇到HTML结束标签，立即停止
              if (completeResponse.includes("</html>")) {
                const estimatedLines = Math.ceil(completeResponse.length / 60);
                console.log(`🎯 检测到</html>标签，立即停止生成: ${estimatedLines}行, ${completeResponse.length}字符`);
                break;
              }

              // 检查是否真正完成HTML生成（智能判断完成条件）
              if (finishReason === 'stop' || finishReason === 'length') {
                const estimatedLines = Math.ceil(completeResponse.length / 60);
                
                // 如果内容足够长，允许结束
                if (estimatedLines >= 600) {
                  console.log(`🎯 生成完成: ${estimatedLines}行, ${completeResponse.length}字符`);
                  break;
                } else if (finishReason === 'length') {
                  // 如果因为长度限制结束，强制补全HTML
                  console.log(`⚠️ 达到长度限制(${estimatedLines}行)，补全HTML结构...`);
                  if (!completeResponse.includes("</body>")) {
                    const bodyEnd = "\n</body>";
                    completeResponse += bodyEnd;
                    await writer.write(encoder.encode(bodyEnd));
                  }
                  const htmlEnd = "\n</html>";
                  completeResponse += htmlEnd;
                  await writer.write(encoder.encode(htmlEnd));
                  break;
                } else {
                  console.log(`⚠️ 内容不够长(${estimatedLines}行)，继续生成...`);
                  // 继续生成，不要break
                }
              }
            } else {
              const lastThinkTagIndex =
                completeResponse.lastIndexOf("</think>");
              completeResponse += content;
              await writer.write(encoder.encode(content));
              if (lastThinkTagIndex !== -1) {
                const afterLastThinkTag = completeResponse.slice(
                  lastThinkTagIndex + "</think>".length
                );
                if (afterLastThinkTag.includes("</html>") || finishReason === 'stop' || finishReason === 'length') {
                  break;
                }
              }
            }
          }
          
          // 如果没有内容但有完成标志，也要退出
          if (!content && (finishReason === 'stop' || finishReason === 'length')) {
            break;
          }
        }
        
        // 清除超时
        clearTimeout(timeoutId);
        
        // 如果没有超时，确保HTML完整性和清理
        if (!isTimedOut && completeResponse) {
          // 🔧 强化的最终清理：移除开头的任何多余文本
          console.log('🧹 开始强化清理...');
          
          // 🔧 Step 1: 移除开头的单独"html"文本行
          if (completeResponse.trim().startsWith('html\n') || completeResponse.trim().startsWith('html\r\n')) {
            console.log('🧹 移除开头的"html"文本行');
            completeResponse = completeResponse.replace(/^html\s*[\r\n]+/i, '');
          }
          
          // 🔧 Step 2: 如果不是以<!DOCTYPE开头，寻找并截取到DOCTYPE
          if (!completeResponse.trim().startsWith('<!DOCTYPE')) {
            const doctypeIndex = completeResponse.indexOf('<!DOCTYPE');
            if (doctypeIndex > 0) {
              console.log('🧹 清理DOCTYPE之前的所有多余文本');
              completeResponse = completeResponse.substring(doctypeIndex);
            }
          }
          
          // 🔧 Step 3: 清理Markdown代码块标记
          if (completeResponse.includes('```html')) {
            console.log('🧹 检测到Markdown代码块标记，进行清理...');
            // 移除开头的 ```html
            completeResponse = completeResponse.replace(/^```html\s*\n?/i, '');
            // 移除结尾的 ```
            completeResponse = completeResponse.replace(/\n?\s*```\s*$/i, '');
            console.log('✅ Markdown标记清理完成');
          }
          
          console.log('✅ 强化清理完成');
          
          // 检查是否有HTML结束标签后的额外内容，如果有则截断
          const htmlEndIndex = completeResponse.indexOf("</html>");
          if (htmlEndIndex !== -1) {
            const cleanResponse = completeResponse.substring(0, htmlEndIndex + 7); // 保留</html>
            if (cleanResponse.length < completeResponse.length) {
              console.log(`🧹 清理HTML结束后的额外内容: 从${completeResponse.length}字符截断到${cleanResponse.length}字符`);
              completeResponse = cleanResponse;
            }
          } else if (!completeResponse.includes("</html>")) {
            console.log('🔧 补全未完成的HTML...');
            if (!completeResponse.includes("</body>")) {
              const bodyEnd = "\n</body>";
              completeResponse += bodyEnd;
              await writer.write(encoder.encode(bodyEnd));
            }
            const htmlEnd = "\n</html>";
            completeResponse += htmlEnd;
            await writer.write(encoder.encode(htmlEnd));
          }
        }

        // 🔧 处理SEARCH/REPLACE格式的响应
        if (html && completeResponse.includes('<<<<<<< SEARCH') && completeResponse.includes('>>>>>>> REPLACE')) {
          console.log('🎯 检测到SEARCH/REPLACE格式，应用精确修改...');
          console.log('🔍 解析SEARCH/REPLACE块...');
          
          // 🔧 修复：过滤掉AI响应中的多余内容，只保留SEARCH/REPLACE块
          const cleanContent = completeResponse
            .replace(/^[\s\S]*?(?=<{7}\s*SEARCH)/m, '') // 移除开头的多余文字
            .replace(/<\/body>\s*<\/html>\s*$/i, '') // 移除结尾的HTML标签
            .trim();
          
          console.log('🔧 清理后的内容长度:', cleanContent.length);
          
          const modifiedHtml = applySearchReplaceBlocks(html, cleanContent);
          
          if (modifiedHtml && modifiedHtml !== html) {
            console.log('✅ SEARCH/REPLACE应用成功，替换响应内容');
            console.log('📝 响应已更新为修改后的HTML');
            completeResponse = modifiedHtml;
          } else {
            console.log('⚠️ SEARCH/REPLACE处理失败，保持原始响应');
          }
        }

        // 🎯 AI请求成功完成，扣除积分
        if (pointsCheck.pointsCost > 0) {
          const chargeResult = await chargePointsForAIRequest(
            user.id,
            selectedModel.value,
            undefined, // chatHistoryId - POST请求通常不直接关联聊天记录
            {
              request_type: 'website_generation',
              model_key: selectedModel.value,
              provider: provider,
              has_images: !!(images && images.length > 0),
              prompt_length: (prompt || redesignMarkdown || '').length,
              response_length: completeResponse.length
            }
          );

          if (chargeResult.success) {
            console.log('✅ AI请求积分扣除成功:', {
              userId: user.id,
              modelKey: selectedModel.value,
              pointsCharged: chargeResult.pointsCharged,
              transactionId: chargeResult.transactionId
            });
          } else {
            console.error('❌ AI请求积分扣除失败，但请求已完成:', {
              userId: user.id,
              modelKey: selectedModel.value,
              pointsCost: pointsCheck.pointsCost
            });
          }
        }

        // 确保流正确关闭
        console.log('🎯 网站生成完成，关闭流式响应');
        if (!writer.closed) {
          await writer.close();
        }

        // 🔧 清理生成状态
        generationCache.delete(cacheKey);
      } catch (error: any) {
        // 🔍 检测AI错误类型，决定是否扣除积分
        const aiError = detectAIError(error);

        console.log('❌ AI请求发生错误:', {
          errorMessage: aiError.errorMessage,
          isModelBusy: aiError.isModelBusy,
          isNetworkError: aiError.isNetworkError,
          isQuotaExceeded: aiError.isQuotaExceeded,
          shouldChargePoints: aiError.shouldChargePoints,
          userId: user.id,
          modelKey: selectedModel.value
        });

        // 只有在非系统错误时才扣除积分
        if (aiError.shouldChargePoints && pointsCheck.pointsCost > 0) {
          console.log('⚠️ 非系统错误，仍需扣除积分');
          await chargePointsForAIRequest(
            user.id,
            selectedModel.value,
            undefined,
            {
              request_type: 'website_generation_failed',
              model_key: selectedModel.value,
              provider: provider,
              error_type: 'user_error',
              error_message: aiError.errorMessage
            }
          );
        } else {
          console.log('✅ 系统错误，不扣除积分:', {
            errorType: aiError.isModelBusy ? 'model_busy' :
                      aiError.isNetworkError ? 'network_error' :
                      aiError.isQuotaExceeded ? 'quota_exceeded' : 'unknown'
          });
        }

        if (error.message?.includes("exceeded") || error.message?.includes("quota")) {
          await writer.write(
            encoder.encode(
              JSON.stringify({
                ok: false,
                openProModal: true,
                message: error.message,
              })
            )
          );
        } else {
          // 根据错误类型提供更友好的错误信息
          let userMessage = error.message || "An error occurred while processing your request.";

          if (aiError.isModelBusy) {
            userMessage = "AI模型当前繁忙，请稍后再试（未扣除积分）";
          } else if (aiError.isNetworkError) {
            userMessage = "网络连接异常，请检查网络后重试（未扣除积分）";
          } else if (aiError.isQuotaExceeded) {
            userMessage = "API配额不足，请联系管理员（未扣除积分）";
          }

          await writer.write(
            encoder.encode(
              JSON.stringify({
                ok: false,
                message: userMessage,
              })
            )
          );
        }
      } finally {
        try {
          // 只有在没有超时的情况下才尝试关闭writer（超时时已经关闭了）
          if (!isTimedOut && writer && !writer.closed) {
            await writer.close();
          }
        } catch (error) {
          console.warn('Writer already closed or error closing:', error);
        } finally {
          // 🔧 清理生成状态
          generationCache.delete(cacheKey);
        }
      }
    })();

    return response;
  } catch (error: any) {
    return NextResponse.json(
      {
        ok: false,
        openSelectProvider: true,
        message:
          error?.message || "An error occurred while processing your request.",
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  const authHeaders = await headers();

  // 🔐 检查用户认证状态 - 必须登录才能使用
  const cookieHeader = authHeaders.get('cookie');
  const token = cookieHeader?.split(';')
    .find(c => c.trim().startsWith('loomrun_token='))
    ?.split('=')[1];

  if (!token) {
    console.log('⚠️ ask-ai PUT: 用户未登录，拒绝请求');
    return NextResponse.json(
      {
        ok: false,
        openLogin: true,
        message: "请登录后使用AI生成功能"
      },
      { status: 401 }
    );
  }

  // 获取用户信息
  const user = await getUserByToken(token);
  if (!user) {
    console.log('⚠️ ask-ai PUT: 无效的用户token');
    return NextResponse.json(
      {
        ok: false,
        openLogin: true,
        message: "用户认证失败，请重新登录"
      },
      { status: 401 }
    );
  }

  const body = await request.json();
  const { prompt, html, previousPrompt, selectedElementHtml, elementContext, chatHistory, provider, model, images } = body;

  if (!prompt || !html) {
    return NextResponse.json(
      { ok: false, error: "Missing required fields" },
      { status: 400 }
    );
  }

  // 🔧 关键修复：使用传入的模型参数
  const providerKey = provider || "deepseek-official";
  const selectedProvider = PROVIDERS[providerKey as keyof typeof PROVIDERS];
  let selectedModel = model || "deepseek-chat";
  
  console.log('🔧 PUT请求模型配置', {
    provider: providerKey,
    model: selectedModel,
    hasImages: images && images.length > 0
  });

  // 🔧 根据模型选择API密钥
  const apiKey = selectedModel.includes('doubao') ? process.env.DOUBAO_API_KEY : process.env.DEEPSEEK_API_KEY;
  
  if (!apiKey) {
    return NextResponse.json(
      {
        ok: false,
        message: `${selectedModel.includes('doubao') ? 'Doubao' : 'DeepSeek'} API key is required. Please add ${selectedModel.includes('doubao') ? 'DOUBAO_API_KEY' : 'DEEPSEEK_API_KEY'} to your environment variables.`,
      },
      { status: 500 }
    );
  }

  // 🔍 积分检查 - 在AI请求前检查用户积分是否足够
  const pointsCheck = await preCheckAndReservePoints(user.id, selectedModel);
  if (!pointsCheck.canProceed) {
    console.log('⚠️ PUT请求用户积分不足:', {
      userId: user.id,
      modelKey: selectedModel,
      pointsCost: pointsCheck.pointsCost,
      currentPoints: pointsCheck.currentPoints,
      errorMessage: pointsCheck.errorMessage
    });

    return NextResponse.json(
      {
        ok: false,
        openProModal: true,
        message: pointsCheck.errorMessage || "积分不足，请充值后使用",
        pointsRequired: pointsCheck.pointsCost,
        currentPoints: pointsCheck.currentPoints
      },
      { status: 402 } // Payment Required
    );
  }

  console.log('✅ PUT请求积分检查通过:', {
    userId: user.id,
    modelKey: selectedModel,
    pointsCost: pointsCheck.pointsCost,
    currentPoints: pointsCheck.currentPoints
  });

  // 🔧 根据模型选择客户端 - 临时回退机制
  let client;
  if (selectedModel.includes('doubao')) {
    console.log('🔧 PUT请求: 尝试使用豆包客户端');
    try {
      client = new DoubaoClient(apiKey);
    } catch (error) {
      console.warn('⚠️ 豆包客户端初始化失败，回退到DeepSeek:', error);
      // 回退到DeepSeek
      const deepseekApiKey = process.env.DEEPSEEK_API_KEY;
      if (deepseekApiKey) {
        client = new DeepSeekClient(deepseekApiKey);
        console.log('🔄 已回退到DeepSeek客户端');
      } else {
        throw new Error('豆包和DeepSeek API密钥都不可用');
      }
    }
  } else {
    client = new DeepSeekClient(apiKey);
  }

  try {
    // 🔧 构建消息历史 - 根据模型类型使用不同的提示词
    const isDoubaoModel = selectedModel.includes('doubao');
    const systemPrompt = isDoubaoModel 
      ? DOUBAO_FOLLOW_UP_SYSTEM_PROMPT + getLanguagePrompt()
      : FOLLOW_UP_SYSTEM_PROMPT + getLanguagePrompt();
    
    const messages: Array<{role: "system" | "user" | "assistant", content: string}> = [
      {
        role: "system",
        content: systemPrompt,
      }
    ];

    // 🔧 如果有聊天历史，添加到消息中（豆包模型现在可以处理更多历史）
    if (chatHistory && Array.isArray(chatHistory) && chatHistory.length > 0) {
      console.log('🔧 PUT请求: 添加聊天历史上下文', { historyLength: chatHistory.length });
      
      // 豆包模型现在可以处理更多历史，DeepSeek保持原有限制
      const maxHistory = isDoubaoModel ? 20 : 10; // 豆包10轮对话 vs DeepSeek 5轮对话
      const recentHistory = chatHistory.slice(-maxHistory);
      
      for (const msg of recentHistory) {
        if (msg.type === 'user' && msg.content) {
          messages.push({
            role: "user",
            content: msg.content
          });
        } else if (msg.type === 'ai' && msg.content) {
          // 豆包模型现在可以处理完整的AI回复
          messages.push({
            role: "assistant",
            content: msg.content
          });
        }
      }
    } else {
      // 🔧 兼容原有的previousPrompt逻辑 - 根据模型类型使用不同的fallback
      const fallbackPrompt = isDoubaoModel ? DOUBAO_PUT_USER_FALLBACK : PUT_USER_FALLBACK;
      messages.push({
        role: "user",
        content: previousPrompt || fallbackPrompt,
      });
    }

    // 🔧 添加当前HTML上下文 - 根据模型类型使用不同的构建函数
    const contextPrompt = isDoubaoModel 
      ? buildDoubaoContextPrompt(html, selectedElementHtml || undefined, elementContext || undefined)
      : buildContextPrompt(html, selectedElementHtml || undefined, elementContext || undefined);
    
    messages.push({
      role: "assistant",
      content: contextPrompt,
    });

    // 🔧 添加当前用户请求
    messages.push({
      role: "user",
      content: prompt,
    });

    console.log('🔧 PUT请求: 构建消息历史完成', {
      totalMessages: messages.length,
      hasChatHistory: !!chatHistory,
      hasSelectedElement: !!selectedElementHtml
    });

    console.log('🚀 PUT请求: 开始API调用', {
      model: selectedModel,
      provider: providerKey,
      isDoubaoModel,
      maxTokens: selectedProvider.max_tokens,
      messagesCount: messages.length
    });

    // 🔧 根据模型类型设置正确的max_tokens限制
    const maxTokens = isDoubaoModel ? 
      Math.min(selectedProvider.max_tokens || 16384, 16384) : // 豆包最大16384
      Math.min(selectedProvider.max_tokens || 8192, 8192); // DeepSeek最大8192
    
    let response: any;
    try {
      
      response = await client.chatCompletion({
        model: selectedModel,
        messages,
        max_tokens: maxTokens,
      });

      console.log('✅ PUT请求: API调用成功', {
        hasResponse: !!response,
        hasChoices: !!response.choices,
        choicesLength: response.choices?.length || 0
      });
    } catch (apiError: any) {
      console.error('❌ PUT请求: API调用失败', {
        error: apiError.message,
        model: selectedModel,
        provider: providerKey,
        isNetworkError: apiError.message?.includes('fetch failed') || apiError.message?.includes('timeout'),
        isDoubaoModel
      });
      
      // 🔧 豆包API连接失败时自动回退到DeepSeek
      if (isDoubaoModel && (apiError.message?.includes('fetch failed') || 
                           apiError.message?.includes('timeout') || 
                           apiError.message?.includes('Connect Timeout'))) {
        console.log('🔄 豆包API连接失败，自动回退到DeepSeek...');
        
        const deepseekApiKey = process.env.DEEPSEEK_API_KEY;
        if (deepseekApiKey) {
          try {
            console.log('🔧 使用DeepSeek客户端重试...');
            const deepseekClient = new DeepSeekClient(deepseekApiKey);
            
            // 使用DeepSeek的提示词和限制
            const deepseekSystemPrompt = FOLLOW_UP_SYSTEM_PROMPT + getLanguagePrompt();
            const deepseekMessages = [
              { role: "system" as const, content: deepseekSystemPrompt },
              ...messages.slice(1).map(msg => ({
                ...msg,
                content: msg.content.replace(/豆包/g, 'DeepSeek') // 替换提示词中的模型名称
              }))
            ];
            
            response = await deepseekClient.chatCompletion({
              model: "deepseek-chat",
              messages: deepseekMessages,
              max_tokens: Math.min(8192, maxTokens),
            });
            
            console.log('✅ DeepSeek回退成功', {
              hasResponse: !!response,
              hasChoices: !!response.choices,
              choicesLength: response.choices?.length || 0
            });
            
            // 更新标记，后续处理使用DeepSeek的格式
            selectedModel = "deepseek-chat";
          } catch (deepseekError: any) {
            console.error('❌ DeepSeek回退也失败:', deepseekError.message);
            throw new Error(`豆包API连接失败，DeepSeek回退也失败: ${deepseekError.message}`);
          }
        } else {
          throw new Error('豆包API连接失败，且未配置DeepSeek API密钥作为备用');
        }
      } else {
        throw apiError;
      }
    }

    const chunk = response.choices[0]?.message?.content;
    console.log('🔍 AI Response:', chunk);
    
    if (!chunk) {
      return NextResponse.json(
        { ok: false, message: "No content returned from the model" },
        { status: 400 }
      );
    }

    if (chunk) {
      const updatedLines: number[][] = [];
      let newHtml = html;
      let position = 0;
      let moreBlocks = true;
      let blocksProcessed = 0;

      console.log('🚀 开始处理搜索替换块...');
      
      // 🔧 根据模型类型使用不同的标记
      const SEARCH_START_MARKER = isDoubaoModel ? DOUBAO_SEARCH_START : SEARCH_START;
      const DIVIDER_MARKER = isDoubaoModel ? DOUBAO_DIVIDER : DIVIDER;
      const REPLACE_END_MARKER = isDoubaoModel ? DOUBAO_REPLACE_END : REPLACE_END;
      
      while (moreBlocks) {
        const searchStartIndex = chunk.indexOf(SEARCH_START_MARKER, position);
        if (searchStartIndex === -1) {
          console.log('📋 未找到更多搜索块，处理完成');
          moreBlocks = false;
          continue;
        }

        const dividerIndex = chunk.indexOf(DIVIDER_MARKER, searchStartIndex);
        if (dividerIndex === -1) {
          console.log('❌ 未找到分隔符，跳过此块');
          moreBlocks = false;
          continue;
        }

        const replaceEndIndex = chunk.indexOf(REPLACE_END_MARKER, dividerIndex);
        if (replaceEndIndex === -1) {
          console.log('❌ 未找到结束标记，跳过此块');
          moreBlocks = false;
          continue;
        }

        const searchBlock = chunk.substring(
          searchStartIndex + SEARCH_START_MARKER.length,
          dividerIndex
        ).trim();
        const replaceBlock = chunk.substring(
          dividerIndex + DIVIDER_MARKER.length,
          replaceEndIndex
        ).trim();

        console.log(`🔎 搜索块 ${blocksProcessed + 1}:`, {
          searchBlock: searchBlock.substring(0, 100) + (searchBlock.length > 100 ? '...' : ''),
          replaceBlock: replaceBlock.substring(0, 100) + (replaceBlock.length > 100 ? '...' : ''),
          searchLength: searchBlock.length,
          replaceLength: replaceBlock.length
        });

        // 🔧 预处理：移除临时UI状态类
        const cleanSearchBlock = searchBlock.replace(/\s*(hovered-element|selected-element|hover|selected)\s*/g, '').replace(/\s+/g, ' ').trim();
        const cleanHtml = newHtml.replace(/\s*(hovered-element|selected-element|hover|selected)\s*/g, '').replace(/\s+/g, ' ').trim();

        if (searchBlock === "") {
          console.log('➕ 空搜索块，在开头插入内容');
          newHtml = `${replaceBlock}\n${newHtml}`;
          updatedLines.push([1, replaceBlock.split("\n").length]);
          blocksProcessed++;
        } else {
          // 🔧 改进的搜索匹配算法
          let blockPosition = -1;
          let matchFound = false;
          let actualSearchBlock = searchBlock;
          
          // 1. 首先尝试精确匹配
          blockPosition = newHtml.indexOf(searchBlock);
          if (blockPosition !== -1) {
            console.log(`✅ 精确匹配找到位置: ${blockPosition}`);
            matchFound = true;
          } else if (cleanSearchBlock !== searchBlock) {
            // 1.5. 尝试使用清理后的搜索块匹配
            blockPosition = cleanHtml.indexOf(cleanSearchBlock);
            if (blockPosition !== -1) {
              console.log(`✅ 清理后匹配成功: ${blockPosition}`);
              // 在原HTML中找到对应位置
              const originalPosition = newHtml.indexOf(searchBlock.replace(/\s*(hovered-element|selected-element|hover|selected)\s*/g, '').trim());
              if (originalPosition !== -1) {
                blockPosition = originalPosition;
                actualSearchBlock = searchBlock.replace(/\s*(hovered-element|selected-element|hover|selected)\s*/g, '').trim();
                matchFound = true;
              }
            }
          } else {
            console.log('🔍 精确匹配失败，尝试智能匹配...');
            
            // 2. 尝试去除前后空白的匹配
            const trimmedSearch = searchBlock.trim();
            blockPosition = newHtml.indexOf(trimmedSearch);
            if (blockPosition !== -1) {
              console.log('✅ 去空白匹配成功');
              actualSearchBlock = trimmedSearch;
              matchFound = true;
            } else {
              // 3. 尝试标准化空白字符的匹配
              const normalizedSearch = searchBlock.replace(/\s+/g, ' ').trim();
              const lines = newHtml.split('\n');
              
              for (let i = 0; i < lines.length; i++) {
                const normalizedLine = lines[i].replace(/\s+/g, ' ').trim();
                if (normalizedLine === normalizedSearch) {
                  // 找到匹配行，使用原始行内容
                  actualSearchBlock = lines[i];
                  blockPosition = newHtml.indexOf(actualSearchBlock);
                  if (blockPosition !== -1) {
                    console.log(`✅ 行级标准化匹配成功，行号: ${i + 1}`);
                    matchFound = true;
                    break;
                  }
                }
              }
              
              // 4. 如果还是找不到，尝试部分匹配（针对文本内容）
              if (!matchFound && searchBlock.includes('>') && searchBlock.includes('<')) {
                // 提取标签内的文本内容
                const textMatch = searchBlock.match(/>([^<]+)</);
                if (textMatch && textMatch[1]) {
                  const textContent = textMatch[1].trim();
                  console.log('🔍 尝试文本内容匹配:', textContent);
                  
                  // 在HTML中查找包含此文本的标签
                  const regex = new RegExp(`<[^>]*>${textContent.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}<\/[^>]*>`, 'g');
                  const matches = Array.from(newHtml.matchAll(regex));
                  
                  if (matches.length > 0) {
                    // 使用第一个匹配项
                    const match = matches[0] as RegExpExecArray;
                    if (match && match[0] && typeof match.index === 'number') {
                      actualSearchBlock = match[0];
                      blockPosition = match.index;
                      console.log('✅ 文本内容匹配成功', {
                        searchedText: textContent,
                        foundElement: actualSearchBlock,
                        position: blockPosition
                      });
                      matchFound = true;
                    }
                  } else {
                    // 如果正则匹配失败，使用原来的逻辑作为备选
                    const textPosition = newHtml.indexOf(textContent);
                    if (textPosition !== -1) {
                      // 找到文本，定位到包含的标签
                      const beforeText = newHtml.substring(0, textPosition);
                      const lastTagStart = beforeText.lastIndexOf('<');
                      const afterText = newHtml.substring(textPosition);
                      const nextTagEnd = afterText.indexOf('>') + textPosition + 1;
                      
                      if (lastTagStart !== -1 && nextTagEnd > textPosition) {
                        actualSearchBlock = newHtml.substring(lastTagStart, nextTagEnd);
                        blockPosition = lastTagStart;
                        console.log('✅ 文本内容备选匹配成功');
                        matchFound = true;
                      }
                    }
                  }
                }
              }
              
              // 5. 最后尝试模糊匹配（使用搜索块的前50个字符）
              if (!matchFound && searchBlock.length > 50) {
                const partialSearch = searchBlock.substring(0, 50).trim();
                blockPosition = newHtml.indexOf(partialSearch);
                if (blockPosition !== -1) {
                  // 尝试扩展到完整的标签或行
                  const startPos = blockPosition;
                  let endPos = blockPosition + partialSearch.length;
                  
                  // 如果是HTML标签，扩展到标签结束
                  if (newHtml[startPos] === '<') {
                    const tagEnd = newHtml.indexOf('>', endPos);
                    if (tagEnd !== -1) {
                      endPos = tagEnd + 1;
                    }
                  }
                  
                  actualSearchBlock = newHtml.substring(startPos, endPos);
                  console.log('✅ 部分匹配成功，长度:', actualSearchBlock.length);
                  matchFound = true;
                }
              }
            }
          }
          
          if (matchFound && blockPosition !== -1) {
            console.log(`✅ 执行替换，位置: ${blockPosition}`);
            const beforeText = newHtml.substring(0, blockPosition);
            const startLineNumber = beforeText.split("\n").length;
            const replaceLines = replaceBlock.split("\n").length;
            const endLineNumber = startLineNumber + replaceLines - 1;

            updatedLines.push([startLineNumber, endLineNumber]);
            
            // 🔧 安全替换：确保只替换第一个匹配项
            const beforeMatch = newHtml.substring(0, blockPosition);
            const afterMatch = newHtml.substring(blockPosition + actualSearchBlock.length);
            newHtml = beforeMatch + replaceBlock + afterMatch;
            
            blocksProcessed++;
            console.log(`✨ 替换完成，更新行数: ${startLineNumber}-${endLineNumber}`);
          } else {
            console.log('❌ 所有匹配方法都失败，跳过此替换块');
            console.log('搜索块内容:', searchBlock.substring(0, 200));
            console.log('原HTML片段:', newHtml.substring(0, 500));
          }
        }

        position = replaceEndIndex + REPLACE_END_MARKER.length;
      }

      console.log(`🎯 处理完成: 共处理 ${blocksProcessed} 个替换块`);
      console.log('📏 原HTML长度:', html.length, '新HTML长度:', newHtml.length);
      
      // 🔧 验证HTML完整性
      if (newHtml.length < html.length * 0.5) {
        console.warn('⚠️ 新HTML长度异常短，可能出现截断问题');
        return NextResponse.json({
          ok: false,
          message: "检测到HTML内容可能被意外截断，为安全起见已取消此次修改。请尝试更具体的修改指令。",
        }, { status: 400 });
      }
      
      // 确保HTML基本结构完整
      if (html.includes('<!DOCTYPE html>') && !newHtml.includes('<!DOCTYPE html>')) {
        console.warn('⚠️ 检测到DOCTYPE声明丢失');
        return NextResponse.json({
          ok: false,
          message: "检测到HTML结构可能损坏，已取消修改。请尝试更具体的修改指令。",
        }, { status: 400 });
      }
      
      // 如果没有处理任何块，尝试备用处理
      if (blocksProcessed === 0) {
        console.log('⚠️ 未处理任何搜索替换块，尝试备用处理...');
        
        // 检查是否包含HTML代码
        const htmlMatch = chunk.match(/```html\s*([\s\S]*?)\s*```/);
        if (htmlMatch) {
          console.log('🔄 发现HTML代码块，使用作为完整替换');

          // 扣除积分（备用处理成功）
          if (pointsCheck.pointsCost > 0) {
            await chargePointsForAIRequest(
              user.id,
              selectedModel,
              undefined,
              {
                request_type: 'website_modification_fallback',
                model_key: selectedModel,
                provider: providerKey,
                fallback_mode: 'html_code_block'
              }
            );
          }

          return NextResponse.json({
            ok: true,
            html: htmlMatch[1].trim(),
            updatedLines: [[1, htmlMatch[1].trim().split('\n').length]],
            blocksProcessed: 1,
            fallbackMode: true
          });
        }
        
        // 如果response看起来像是直接的HTML，使用它
        if (chunk.includes('<section') || chunk.includes('<div') || chunk.includes('class=')) {
          console.log('🎯 响应看起来像直接的HTML代码，尝试应用');
          const cleanedChunk = chunk.replace(/^[^<]*/, '').replace(/[^>]*$/, '');
          if (cleanedChunk.length > 50) {
            // 扣除积分（备用处理成功）
            if (pointsCheck.pointsCost > 0) {
              await chargePointsForAIRequest(
                user.id,
                selectedModel,
                undefined,
                {
                  request_type: 'website_modification_fallback',
                  model_key: selectedModel,
                  provider: providerKey,
                  fallback_mode: 'direct_html'
                }
              );
            }

            return NextResponse.json({
              ok: true,
              html: cleanedChunk,
              updatedLines: [[1, cleanedChunk.split('\n').length]],
              blocksProcessed: 1,
              fallbackMode: true
            });
          }
        }
        
        // 如果仍然没有找到有效内容，返回错误
        return NextResponse.json({
          ok: false,
          message: "AI响应格式不正确，请重新尝试。确保使用具体的修改指令。如果您选择的是图表或动态生成的元素，请选择包含该元素的容器进行修改。",
        }, { status: 400 });
      }
      
      // 🎯 PUT请求成功完成，扣除积分
      if (pointsCheck.pointsCost > 0) {
        const chargeResult = await chargePointsForAIRequest(
          user.id,
          selectedModel,
          undefined, // chatHistoryId - PUT请求通常不直接关联聊天记录
          {
            request_type: 'website_modification',
            model_key: selectedModel,
            provider: providerKey,
            has_images: !!(images && images.length > 0),
            prompt_length: prompt.length,
            html_length: html.length,
            blocks_processed: blocksProcessed,
            updated_lines: updatedLines.length
          }
        );

        if (chargeResult.success) {
          console.log('✅ PUT请求积分扣除成功:', {
            userId: user.id,
            modelKey: selectedModel,
            pointsCharged: chargeResult.pointsCharged,
            transactionId: chargeResult.transactionId
          });
        } else {
          console.error('❌ PUT请求积分扣除失败，但请求已完成:', {
            userId: user.id,
            modelKey: selectedModel,
            pointsCost: pointsCheck.pointsCost
          });
        }
      }

      return NextResponse.json({
        ok: true,
        html: newHtml,
        updatedLines,
        blocksProcessed
      });
    }
  } catch (error: any) {
    console.error('PUT request error:', error);

    // 🔍 检测AI错误类型，决定是否扣除积分
    const aiError = detectAIError(error);

    console.log('❌ PUT请求发生错误:', {
      errorMessage: aiError.errorMessage,
      isModelBusy: aiError.isModelBusy,
      isNetworkError: aiError.isNetworkError,
      isQuotaExceeded: aiError.isQuotaExceeded,
      shouldChargePoints: aiError.shouldChargePoints,
      userId: user.id,
      modelKey: selectedModel
    });

    // 只有在非系统错误时才扣除积分
    if (aiError.shouldChargePoints && pointsCheck.pointsCost > 0) {
      console.log('⚠️ 非系统错误，仍需扣除积分');
      await chargePointsForAIRequest(
        user.id,
        selectedModel,
        undefined,
        {
          request_type: 'website_modification_failed',
          model_key: selectedModel,
          provider: providerKey,
          error_type: 'user_error',
          error_message: aiError.errorMessage
        }
      );
    } else {
      console.log('✅ 系统错误，不扣除积分:', {
        errorType: aiError.isModelBusy ? 'model_busy' :
                  aiError.isNetworkError ? 'network_error' :
                  aiError.isQuotaExceeded ? 'quota_exceeded' : 'unknown'
      });
    }

    // 根据错误类型提供更友好的错误信息
    let userMessage = error.message || "处理请求时发生错误";

    if (aiError.isModelBusy) {
      userMessage = "AI模型当前繁忙，请稍后再试（未扣除积分）";
    } else if (aiError.isNetworkError) {
      userMessage = "网络连接异常，请检查网络后重试（未扣除积分）";
    } else if (aiError.isQuotaExceeded) {
      userMessage = "API配额不足，请联系管理员（未扣除积分）";
    }

    return NextResponse.json(
      { ok: false, message: userMessage },
      { status: 500 }
    );
  }
}

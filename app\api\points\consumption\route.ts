import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { getUserByToken } from "@/lib/auth-service";
import { executeQuery } from "@/lib/mysql";
import { initDatabase } from "@/lib/mysql";

// 模型名称映射 - 将API模型名称转换为统一的显示名称
const MODEL_DISPLAY_MAPPING: Record<string, string> = {
  // DeepSeek 模型映射
  'deepseek-chat': 'loomrun_1.2ds',
  'deepseek-coder': 'loomrun_1.2ds',
  'deepseek-reasoner': 'loomrun_1.2ds',

  // 豆包模型映射
  'doubao-lite-4k': 'loomrun_1.6db',
  'doubao-pro-4k': 'loomrun_1.6db',
  'doubao-pro-32k': 'loomrun_1.6db',
  'doubao-pro-128k': 'loomrun_1.6db',
  'doubao-seed-1-6-250615': 'loomrun_1.6db',
  'doubao-seed-1-6-250616': 'loomrun_1.6db',
  'doubao-seed-1-6-250617': 'loomrun_1.6db',

  // 其他可能的豆包模型变体
  'ep-20241230140251-8xqzx': 'loomrun_1.6db',
  'ep-20241230140251-xxxxx': 'loomrun_1.6db',

  // 数据库中的模型key直接映射
  'loomrun_1.2ds': 'loomrun_1.2ds',
  'loomrun_1.6db': 'loomrun_1.6db',
};

// 获取统一的模型显示名称
const getDisplayModelName = (originalModelName: string): string => {
  // 1. 直接映射
  if (MODEL_DISPLAY_MAPPING[originalModelName]) {
    return MODEL_DISPLAY_MAPPING[originalModelName];
  }

  // 2. 模糊匹配 - 豆包模型
  if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
    return 'loomrun_1.6db';
  }

  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelName.includes('deepseek')) {
    return 'loomrun_1.2ds';
  }

  // 4. 默认情况 - 返回原始名称
  return originalModelName;
};

// 初始化数据库
initDatabase().catch(console.error);

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("loomrun_token")?.value;

    if (!token) {
      return NextResponse.json({ error: "未登录" }, { status: 401 });
    }

    const user = await getUserByToken(token);

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20', 10), 100);
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    const pointsType = searchParams.get('points_type');
    const dateFrom = searchParams.get('date_from');
    const dateTo = searchParams.get('date_to');

    // 先检查表是否存在数据
    try {
      const testQuery = 'SELECT COUNT(*) as count FROM points_consumption_log LIMIT 1';
      const testResult = await executeQuery(testQuery, []) as { count: number }[];

      if (testResult[0]?.count === 0) {
        // 如果没有消费记录，返回空数据但不报错
        return NextResponse.json({
          success: true,
          data: {
            consumptionRecords: [],
            statistics: [],
            pagination: {
              limit,
              offset,
              total: 0,
              hasMore: false,
              currentPage: 1,
              totalPages: 0
            },
            filters: {
              pointsType,
              dateFrom,
              dateTo
            }
          }
        });
      }
    } catch (tableError) {
      console.log('消费记录表可能不存在或为空，返回空数据');
      return NextResponse.json({
        success: true,
        data: {
          consumptionRecords: [],
          statistics: [],
          pagination: {
            limit,
            offset,
            total: 0,
            hasMore: false,
            currentPage: 1,
            totalPages: 0
          },
          filters: {
            pointsType,
            dateFrom,
            dateTo
          }
        }
      });
    }

    // 构建查询条件
    let whereConditions = ['pcl.user_id = ?'];
    let queryParams: (string | number)[] = [user.id];

    if (pointsType && ['activity', 'subscription', 'recharge'].includes(pointsType)) {
      whereConditions.push('pcl.points_type = ?');
      queryParams.push(pointsType);
    }

    if (dateFrom) {
      whereConditions.push('pcl.consumed_at >= ?');
      queryParams.push(dateFrom);
    }

    if (dateTo) {
      whereConditions.push('pcl.consumed_at <= ?');
      queryParams.push(dateTo + ' 23:59:59');
    }

    const whereClause = whereConditions.join(' AND ');

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM points_consumption_log pcl WHERE ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams) as { total: number }[];
    const total = countResult[0]?.total || 0;

    // 获取详细的积分消费记录
    const consumptionQuery = `
      SELECT
        pcl.id,
        pcl.user_id,
        pcl.transaction_id,
        pcl.balance_record_id,
        pcl.points_consumed,
        pcl.points_type,
        pcl.consumed_at,
        pt.source_type,
        pt.description as transaction_description,
        pt.source_id,
        pt.metadata as transaction_metadata,
        upb.expires_at as original_expires_at,
        upb.source_order_id,
        upb.source_plan_key,
        upb.created_at as balance_created_at
      FROM points_consumption_log pcl
      LEFT JOIN points_transactions pt ON pcl.transaction_id = pt.id
      LEFT JOIN user_points_balance upb ON pcl.balance_record_id = upb.id
      WHERE ${whereClause}
      ORDER BY pcl.consumed_at DESC, pcl.id DESC
      LIMIT ? OFFSET ?
    `;

    // 创建新的参数数组，确保类型正确
    const finalQueryParams: (string | number)[] = [...queryParams, limit, offset];
    const consumptionRecords = await executeQuery(consumptionQuery, finalQueryParams);

    // 处理数据
    const processedRecords = consumptionRecords.map((record: any) => {
      // 解析 metadata
      let transactionMetadata = null;
      if (record.transaction_metadata) {
        try {
          transactionMetadata = typeof record.transaction_metadata === 'string'
            ? JSON.parse(record.transaction_metadata)
            : record.transaction_metadata;
        } catch (e) {
          transactionMetadata = record.transaction_metadata;
        }
      }

      // 处理交易描述中的模型名称显示
      let displayTransactionDescription = record.transaction_description;

      if (displayTransactionDescription) {
        // 方法1: 如果有metadata中的model_key，优先使用精确替换
        if (transactionMetadata?.model_key) {
          const displayModelName = getDisplayModelName(transactionMetadata.model_key);

          // 更精确的正则匹配，支持不同的空格情况
          const modelRegex = new RegExp(`AI模型\\s*${transactionMetadata.model_key.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')}`, 'g');
          displayTransactionDescription = displayTransactionDescription.replace(modelRegex, `AI模型 ${displayModelName}`);

          // 也替换单独出现的模型名称
          const modelNameRegex = new RegExp(transactionMetadata.model_key.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g');
          displayTransactionDescription = displayTransactionDescription.replace(modelNameRegex, displayModelName);
        }

        // 方法2: 通用模式替换（作为备用）
        displayTransactionDescription = displayTransactionDescription
          .replace(/doubao-seed-1-6-250615/g, 'loomrun_1.6db')
          .replace(/doubao-seed-1-6-250616/g, 'loomrun_1.6db')
          .replace(/doubao-seed-1-6-250617/g, 'loomrun_1.6db')
          .replace(/doubao-[\w\-]+/g, 'loomrun_1.6db')
          .replace(/deepseek-[\w\-]+/g, 'loomrun_1.2ds')
          .replace(/ep-[\w\-]+/g, 'loomrun_1.6db');

        // 方法3: 通用AI模型模式替换
        displayTransactionDescription = displayTransactionDescription.replace(
          /AI模型\s*([\w\-\.]+)/g,
          (match, modelName) => {
            const displayModelName = getDisplayModelName(modelName);
            return `AI模型 ${displayModelName}`;
          }
        );
      }

      // 计算积分的原始有效期状态
      const wasExpired = record.original_expires_at ?
        new Date(record.original_expires_at) < new Date(record.consumed_at) : false;

      return {
        ...record,
        transaction_metadata: transactionMetadata,
        wasExpired,
        // 添加用户友好的交易描述
        displayTransactionDescription: displayTransactionDescription || record.transaction_description,
        // 添加用户友好的模型名称（如果有的话）
        displayModelName: transactionMetadata?.model_key ? getDisplayModelName(transactionMetadata.model_key) : null,
        formattedConsumedAmount: `-${record.points_consumed}`,
        formattedConsumedDate: new Date(record.consumed_at).toLocaleString('zh-CN'),
        formattedOriginalExpiry: record.original_expires_at ?
          new Date(record.original_expires_at).toLocaleDateString('zh-CN') : '永久有效',
        formattedBalanceCreated: record.balance_created_at ?
          new Date(record.balance_created_at).toLocaleDateString('zh-CN') : null
      };
    });

    // 获取消费统计信息
    const statsQuery = `
      SELECT 
        pcl.points_type,
        COUNT(*) as consumption_count,
        SUM(pcl.points_consumed) as total_consumed,
        MIN(pcl.consumed_at) as first_consumption,
        MAX(pcl.consumed_at) as last_consumption
      FROM points_consumption_log pcl
      WHERE pcl.user_id = ?
      GROUP BY pcl.points_type
      ORDER BY total_consumed DESC
    `;

    const consumptionStats = await executeQuery(statsQuery, [user.id]);

    return NextResponse.json({
      success: true,
      data: {
        consumptionRecords: processedRecords,
        statistics: consumptionStats,
        pagination: {
          limit,
          offset,
          total,
          hasMore: offset + limit < total,
          currentPage: Math.floor(offset / limit) + 1,
          totalPages: Math.ceil(total / limit)
        },
        filters: {
          pointsType,
          dateFrom,
          dateTo
        }
      }
    });

  } catch (error) {
    console.error("获取积分消费记录失败:", error);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}

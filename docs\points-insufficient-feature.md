# 积分不足友好提示功能实现文档

## 功能概述

实现了精准的积分不足友好提示功能，确保在用户积分不够时：
1. **不发送消息到聊天区** - 消息不会被添加到聊天历史
2. **友好提示** - 显示具体的积分需求和当前余额
3. **消息回退** - 将输入的消息退回到输入框，用户可以继续编辑
4. **快速充值** - 提供直接跳转到充值页面的按钮

## 核心实现

### 1. 后端积分检查 (`app/api/ask-ai/route.ts`)

#### POST请求（网站生成）
```typescript
// 🔍 积分检查 - 在AI请求前检查用户积分是否足够
const pointsCheck = await preCheckAndReservePoints(user.id, selectedModel.value);
if (!pointsCheck.canProceed) {
  return NextResponse.json(
    {
      ok: false,
      openProModal: true,
      message: pointsCheck.errorMessage || "积分不足，请充值后使用",
      pointsRequired: pointsCheck.pointsCost,
      currentPoints: pointsCheck.currentPoints
    },
    { status: 402 } // Payment Required
  );
}
```

#### PUT请求（网站修改）
```typescript
// 🔍 积分检查 - 在AI请求前检查用户积分是否足够
const pointsCheck = await preCheckAndReservePoints(user.id, selectedModel);
if (!pointsCheck.canProceed) {
  return NextResponse.json(
    {
      ok: false,
      openProModal: true,
      message: pointsCheck.errorMessage || "积分不足，请充值后使用",
      pointsRequired: pointsCheck.pointsCost,
      currentPoints: pointsCheck.currentPoints
    },
    { status: 402 } // Payment Required
  );
}
```

### 2. 前端错误处理

#### WelcomeLayout (`components/layouts/welcome-layout.tsx`)
```typescript
// 🎯 精准处理积分不足错误（402状态码）
if (response.status === 402) {
  const errorData = await response.json();
  
  // 🔧 关键：停止全局AI状态，防止消息被添加到聊天历史
  globalAIState.stopGeneration();
  
  // 🎯 友好的积分不足提示
  const friendlyMessage = `积分不足！需要 ${errorData.pointsRequired} 积分，当前余额 ${errorData.currentPoints} 积分。请充值后继续使用。`;
  toast.error(friendlyMessage, {
    duration: 5000,
    action: {
      label: '去充值',
      onClick: () => window.open('/points', '_blank')
    }
  });
  
  // 🎯 关键：抛出特殊错误，让上层处理消息回退
  const pointsError = new Error(friendlyMessage) as Error & {
    isPointsInsufficient: boolean;
    originalPrompt: string;
  };
  pointsError.isPointsInsufficient = true;
  pointsError.originalPrompt = prompt;
  throw pointsError;
}
```

#### ChatArea (`components/editor/chat-area/index.tsx`)
```typescript
// 🎯 特殊处理积分不足错误 - 清理聊天历史并回退消息
if (error?.isPointsInsufficient) {
  // 🎯 关键：移除刚添加的用户消息和AI消息
  flushSync(() => {
    setChatHistory(prev => {
      // 移除最后两条消息（用户消息和AI消息）
      const updated = prev.slice(0, -2);
      return updated;
    });
  });
  
  // 🎯 将消息回退到输入框
  setSharedInputPrompt(error.originalPrompt);
  return;
}
```

#### AskAI组件 (`components/editor/ask-ai/index.tsx`)
```typescript
// 🎯 特殊处理积分不足错误 - 将消息回退到输入框
if (error?.isPointsInsufficient) {
  // 🎯 关键：将原始消息回退到输入框
  if (setInputPrompt) {
    setInputPrompt(originalInput);
  } else {
    setInternalInput(originalInput);
  }
  
  // 🎯 重新启动打字机效果（如果是欢迎模式）
  if (isWelcomeMode) {
    setCurrentPlaceholder("");
    setIsTypingActive(false);
  }
  
  return;
}
```

### 3. 聊天历史管理 (`loomrunhooks/useChatHistory.ts`)

新增了清理最后两条消息的方法：
```typescript
// 🎯 清理最后两条消息（用于积分不足时回退）
const clearLastTwoMessages = useCallback(() => {
  setChatHistory(prev => {
    if (prev.length >= 2) {
      const updated = prev.slice(0, -2);
      console.log('🔄 useChatHistory: 已清理最后两条消息', {
        原始消息数: prev.length,
        清理后消息数: updated.length,
        清理的消息: prev.slice(-2).map(msg => ({ id: msg.id, type: msg.type }))
      });
      return updated;
    }
    return prev;
  });
}, []);
```

## 用户体验流程

1. **用户输入消息** - 在输入框中输入要发送给AI的消息
2. **积分检查** - 后端在处理AI请求前先检查用户积分是否足够
3. **积分不足时**：
   - 返回402状态码和详细的积分信息
   - 前端显示友好的错误提示，包含具体的积分需求
   - 提供"去充值"按钮，直接跳转到充值页面
   - **关键**：消息不会被添加到聊天历史
   - **关键**：原始消息会回退到输入框，用户可以继续编辑
4. **积分充足时** - 正常处理AI请求

## 技术特点

1. **精准拦截** - 在AI请求发送前就进行积分检查，避免无效请求
2. **状态一致性** - 使用flushSync确保状态更新的同步性
3. **用户友好** - 提供具体的积分信息和快速充值入口
4. **消息保护** - 确保积分不足时用户输入的内容不会丢失
5. **多场景支持** - 同时支持首页创建和编辑器修改两种场景

## 测试场景

1. **首页创建项目** - 积分不足时消息回退到首页输入框
2. **编辑器修改** - 积分不足时消息回退到编辑器输入框，并清理聊天历史
3. **图片上传** - 带图片的请求积分不足时也能正确处理
4. **不同模型** - 不同AI模型的积分消耗都能正确检查

## 相关文件

- `app/api/ask-ai/route.ts` - 后端积分检查逻辑
- `lib/ai-points-service.ts` - 积分检查服务函数
- `components/layouts/welcome-layout.tsx` - 首页错误处理
- `components/editor/chat-area/index.tsx` - 编辑器错误处理
- `components/editor/ask-ai/index.tsx` - 输入框消息回退
- `loomrunhooks/useChatHistory.ts` - 聊天历史管理

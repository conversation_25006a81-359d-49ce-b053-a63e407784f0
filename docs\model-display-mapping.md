# 模型名称显示映射实现文档

## 问题描述

在积分详情页面中，用户看到的是原始的API模型名称（如 `doubao-seed-1-6-250615`），这对用户来说不够友好。需要将这些技术性的模型名称转换为用户友好的LoomRun品牌名称。

## 解决方案

### 1. API层面的映射处理

在 `app/api/points/history/route.ts` 中实现了模型名称映射逻辑：

#### 映射配置
```typescript
const MODEL_DISPLAY_MAPPING: Record<string, string> = {
  // DeepSeek 模型映射
  'deepseek-chat': 'LoomRun 1.2DS',
  'deepseek-coder': 'LoomRun 1.2DS',
  'deepseek-reasoner': 'LoomRun 1.2DS',
  
  // 豆包模型映射
  'doubao-seed-1-6-250615': 'LoomRun 1.6DB',
  'doubao-pro-4k': 'LoomRun 1.6DB',
  'doubao-pro-32k': 'LoomRun 1.6DB',
  // ... 更多映射
  
  // 数据库中的模型key直接映射
  'loomrun_1.2ds': 'LoomRun 1.2DS',
  'loomrun_1.6db': 'LoomRun 1.6DB',
};
```

#### 智能映射函数
```typescript
const getDisplayModelName = (originalModelName: string): string => {
  // 1. 直接映射
  if (MODEL_DISPLAY_MAPPING[originalModelName]) {
    return MODEL_DISPLAY_MAPPING[originalModelName];
  }
  
  // 2. 模糊匹配 - 豆包模型
  if (originalModelName.includes('doubao') || originalModelName.includes('ep-')) {
    return 'LoomRun 1.6DB';
  }
  
  // 3. 模糊匹配 - DeepSeek模型
  if (originalModelName.includes('deepseek')) {
    return 'LoomRun 1.2DS';
  }
  
  // 4. 默认情况
  return originalModelName;
};
```

### 2. 描述文本的智能替换

API会自动处理积分交易记录的描述文本：

```typescript
// 处理描述中的模型名称显示
let displayDescription = record.description;
if (displayDescription && metadata?.model_key) {
  const displayModelName = getDisplayModelName(metadata.model_key);
  // 替换描述中的模型名称
  displayDescription = displayDescription.replace(
    /AI模型\s+[\w\-\.]+/g, 
    `AI模型 ${displayModelName}`
  );
}

// 如果描述中包含原始模型名称，也进行替换
if (displayDescription) {
  displayDescription = displayDescription
    .replace(/doubao-seed-1-6-250615/g, 'LoomRun 1.6DB')
    .replace(/doubao-[\w\-]+/g, 'LoomRun 1.6DB')
    .replace(/deepseek-[\w\-]+/g, 'LoomRun 1.2DS')
    .replace(/ep-[\w\-]+/g, 'LoomRun 1.6DB');
}
```

### 3. 前端页面的更新

#### 接口定义更新
在 `app/points/page.tsx` 和 `app/points-integrated/page.tsx` 中更新了接口：

```typescript
interface PointsTransaction {
  // ... 其他字段
  description: string;
  displayDescription?: string; // 用户友好的描述
  displayModelName?: string;   // 用户友好的模型名称
  // ... 其他字段
}
```

#### 显示逻辑更新
```typescript
<div className="font-medium">
  {transaction.displayDescription || transaction.description}
</div>
```

## 映射规则

### 直接映射
- `doubao-seed-1-6-250615` → `LoomRun 1.6DB`
- `deepseek-chat` → `LoomRun 1.2DS`
- `loomrun_1.2ds` → `LoomRun 1.2DS`
- `loomrun_1.6db` → `LoomRun 1.6DB`

### 模糊匹配
- 包含 `doubao` 或 `ep-` → `LoomRun 1.6DB`
- 包含 `deepseek` → `LoomRun 1.2DS`

### 描述文本替换
- `AI模型 doubao-seed-1-6-250615 请求消耗` → `AI模型 LoomRun 1.6DB 请求消耗`
- `使用deepseek-chat模型生成内容` → `使用LoomRun 1.2DS模型生成内容`

## 用户体验改进

### 修改前
```
AI模型 doubao-seed-1-6-250615 请求消耗
AI模型 deepseek-chat 请求消耗
```

### 修改后
```
AI模型 LoomRun 1.6DB 请求消耗
AI模型 LoomRun 1.2DS 请求消耗
```

## 技术特点

✅ **向后兼容** - 如果没有映射规则，显示原始名称  
✅ **智能匹配** - 支持直接映射和模糊匹配  
✅ **描述替换** - 自动替换描述文本中的模型名称  
✅ **类型安全** - 更新了TypeScript接口定义  
✅ **可扩展** - 易于添加新的模型映射规则  

## 维护说明

### 添加新模型映射
在 `MODEL_DISPLAY_MAPPING` 中添加新的映射规则：

```typescript
const MODEL_DISPLAY_MAPPING = {
  // ... 现有映射
  'new-model-name': 'LoomRun X.XDB',
};
```

### 测试验证
运行测试脚本验证映射逻辑：
```bash
node scripts/test-display-mapping.js
```

## 影响范围

- ✅ 积分详情页面 (`/points`)
- ✅ 积分集成页面 (`/points-integrated`)
- ✅ 积分历史API (`/api/points/history`)
- ✅ 所有显示积分交易记录的地方

现在用户在积分详情页面将看到友好的LoomRun模型名称，而不是技术性的API模型名称。

# 首页输入框保留问题调试

## 问题分析

从日志可以看出问题的根源：

```
✅ AskAI: onAskAI执行成功，清空输入框
⚠️ WelcomeLayout: 预检查发现积分不足
```

**问题**：AskAI组件认为onAskAI执行成功了，所以清空了输入框，但实际上WelcomeLayout中的预检查发现了积分不足。

## 修复方案

### 1. 修改handleNewPrompt为async函数
```typescript
const handleNewPrompt = useCallback(async (promptText: string, model?: string, images?: string[]) => {
  // 🎯 关键修复：直接调用handleFirstMessage并等待结果，如果积分不足会抛出错误
  await handleFirstMessage(promptText, model, images);
}, [handleFirstMessage]);
```

### 2. 修改handleFirstMessage在积分不足时抛出错误
```typescript
// 🎯 关键修复：积分不足时抛出错误，让AskAI组件知道失败了
console.log('⚠️ WelcomeLayout: 积分不足，抛出错误阻止输入框清空');
setIsCreatingProject(false);

// 抛出特殊错误，让AskAI组件不清空输入框
const pointsError = new Error(`需要 ${errorData.pointsRequired} 积分，当前余额 ${errorData.currentPoints} 积分`);
(pointsError as any).isPointsInsufficient = true;
throw pointsError;
```

### 3. 修改AskAI组件处理积分不足错误
```typescript
// 🎯 特殊处理积分不足错误 - 不清空输入框
if (error?.isPointsInsufficient) {
  console.log('⚠️ AskAI: 捕获积分不足错误，输入框内容保留', {
    errorMessage: error.message
  });
  
  // 积分不足的友好提示已经在WelcomeLayout中处理了
  // 这里不需要重复提示，只需要保持输入框内容不变
  setIsGenerating(false);
  return;
}
```

## 修复后的流程

1. 用户在首页输入框输入消息
2. 点击发送按钮
3. AskAI组件调用onAskAI (handleNewPrompt)
4. handleNewPrompt调用handleFirstMessage
5. handleFirstMessage进行积分预检查
6. **积分不足时**：
   - WelcomeLayout显示友好的蓝色提示
   - 抛出isPointsInsufficient错误
   - AskAI组件捕获错误，不清空输入框
   - 用户看到消息保留在输入框中

## 期望的日志输出

修复后应该看到：
```
🎯 WelcomeLayout: 处理新提示，直接跳转到编辑器: [用户输入]
🚀 WelcomeLayout: 开始处理第一条消息
🔍 WelcomeLayout: 预检查用户积分...
⚠️ WelcomeLayout: 预检查发现积分不足
⚠️ WelcomeLayout: 积分不足，抛出错误阻止输入框清空
⚠️ AskAI: 捕获积分不足错误，输入框内容保留
```

而不应该看到：
```
✅ AskAI: onAskAI执行成功，清空输入框
```

## 测试步骤

1. 确保用户积分为0
2. 在首页输入框输入测试消息
3. 点击发送按钮
4. 观察：
   - 是否显示蓝色信息提示
   - 输入框中的消息是否保留
   - 控制台日志是否正确

## 相关文件

- `components/layouts/welcome-layout.tsx` - handleNewPrompt, handleFirstMessage
- `components/editor/ask-ai/index.tsx` - handleSubmit错误处理

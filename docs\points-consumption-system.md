# 积分消耗系统实现文档

## 系统概述

本系统实现了完整的AI请求积分消耗机制，包括积分预检查、智能消耗、错误处理和详细日志记录。

## 核心特性

### 1. 智能错误检测
- **模型繁忙检测**: 检测AI模型繁忙状态，不扣除积分
- **网络错误检测**: 识别网络连接问题，不扣除积分  
- **配额错误检测**: 检测API配额问题，不扣除积分
- **用户错误处理**: 只有非系统错误才扣除积分

### 2. 积分消耗优先级
系统按以下优先级消耗积分：
1. **subscription** (订阅积分) - 最高优先级
2. **activity** (活动积分) - 中等优先级
3. **recharge** (充值积分) - 最低优先级

在同类型积分中，按过期时间排序（先过期的先消耗）。

### 3. 完整的事务处理
- 使用数据库事务确保数据一致性
- 失败时自动回滚，避免数据不一致
- 支持并发请求的安全处理

## 数据库表结构

### ai_models - AI模型配置
```sql
- model_key: 模型标识符
- model_name: 模型显示名称  
- points_per_request: 每次请求消耗积分数
- is_active: 模型是否激活
```

### points_transactions - 积分交易记录
```sql
- transaction_type: 'earn' | 'spend'
- points_amount: 积分数量
- source_type: 来源类型 (ai_request, export, etc.)
- balance_before/after: 交易前后余额
```

### points_consumption_log - 积分消耗详细日志
```sql
- user_id: 用户ID
- transaction_id: 关联的交易记录
- balance_record_id: 消耗的余额记录
- points_consumed: 消耗的积分数
- points_type: 积分类型
```

### user_points_balance - 用户积分余额
```sql
- points_type: 积分类型 (activity/subscription/recharge)
- points_amount: 余额数量
- expires_at: 过期时间
- is_active: 是否有效
```

## API实现

### POST /api/ask-ai (网站生成)
1. **用户认证检查**
2. **积分预检查** - `preCheckAndReservePoints()`
3. **AI请求处理**
4. **成功时扣除积分** - `chargePointsForAIRequest()`
5. **错误时智能处理** - `detectAIError()`

### PUT /api/ask-ai (网站修改)
1. **用户认证检查**
2. **积分预检查**
3. **AI请求处理**
4. **成功时扣除积分**
5. **错误时智能处理**

## 核心服务函数

### ai-points-service.ts

#### `preCheckAndReservePoints(userId, modelKey)`
- 检查用户积分是否足够
- 获取模型积分消耗配置
- 返回是否可以继续请求

#### `chargePointsForAIRequest(userId, modelKey, chatHistoryId, metadata)`
- 执行实际的积分扣除
- 更新聊天记录的积分消耗字段
- 记录详细的消耗日志

#### `detectAIError(error)`
- 智能检测错误类型
- 判断是否应该扣除积分
- 提供用户友好的错误信息

#### `consumePointsWithPriority(userId, pointsAmount, sourceType, ...)`
- 按优先级智能消耗积分
- 支持多种积分类型混合消耗
- 完整的事务处理

## 错误处理策略

### 不扣积分的情况
- 模型繁忙: "model is busy", "service is busy", "rate limit"
- 网络错误: "fetch failed", "timeout", "connection refused"  
- 配额问题: "quota exceeded", "api key invalid", "unauthorized"

### 扣积分的情况
- 用户输入错误
- 请求格式错误
- 其他非系统性错误

## 使用示例

### 检查用户积分
```javascript
const pointsCheck = await preCheckAndReservePoints(userId, 'deepseek-chat');
if (!pointsCheck.canProceed) {
  return { error: pointsCheck.errorMessage };
}
```

### 扣除积分
```javascript
const chargeResult = await chargePointsForAIRequest(
  userId, 
  'deepseek-chat',
  chatHistoryId,
  { request_type: 'website_generation' }
);
```

### 错误处理
```javascript
try {
  // AI请求
} catch (error) {
  const aiError = detectAIError(error);
  if (aiError.shouldChargePoints) {
    await chargePointsForAIRequest(userId, modelKey);
  }
}
```

## 测试和监控

### 测试脚本
运行 `node scripts/test-points-consumption.js` 检查系统状态：
- AI模型配置
- 用户积分余额
- 交易记录
- 消耗日志
- 统计信息

### 监控指标
- 积分消耗速率
- 错误类型分布
- 用户积分余额趋势
- 模型使用统计

## 配置管理

### 默认数据插入
```sql
-- 运行 scripts/insert_default_data.sql
INSERT INTO ai_models (model_key, model_name, points_per_request, description, display_order) VALUES
('deepseek-chat', 'DeepSeek Chat', 2, 'DeepSeek基础对话模型', 1),
('doubao-pro-4k', 'Doubao Pro 4K', 3, '豆包专业级模型', 4);
```

### 系统设置
- `new_user_points_enabled`: 新用户积分开关
- `new_user_points_amount`: 新用户积分数量

## 性能优化

1. **数据库索引优化**
   - user_id, created_at 复合索引
   - points_type, expires_at 索引
   - transaction_id 外键索引

2. **连接池管理**
   - 使用连接池避免频繁创建连接
   - 事务完成后及时释放连接

3. **缓存策略**
   - 模型配置缓存
   - 用户积分余额缓存

## 安全考虑

1. **事务隔离**
   - 使用数据库事务确保原子性
   - 避免并发操作导致的数据不一致

2. **输入验证**
   - 严格验证用户ID和模型参数
   - 防止SQL注入和参数篡改

3. **错误日志**
   - 详细记录所有积分操作
   - 便于审计和问题排查

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页积分不足测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .expected-behavior {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 首页积分不足功能测试</h1>
        <p>此页面用于测试首页积分不足时的友好提示功能</p>

        <div class="expected-behavior">
            <h4>📋 期望的用户体验：</h4>
            <ol>
                <li>用户在首页输入框中输入消息</li>
                <li>点击发送按钮</li>
                <li>系统进行积分预检查</li>
                <li>如果积分不足：
                    <ul>
                        <li>显示友好的蓝色信息提示：💰 需要 X 积分，当前余额 Y 积分</li>
                        <li>提供"立即充值"按钮</li>
                        <li><strong>关键：消息保留在输入框中，不会被发送出去</strong></li>
                        <li><strong>关键：不显示红色错误，不在控制台报错</strong></li>
                    </ul>
                </li>
                <li>如果积分充足：正常跳转到编辑器页面</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>1. 积分余额查询</h3>
            <button onclick="testPointsBalance()">查询当前积分余额</button>
            <div id="balance-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 首页积分预检查测试</h3>
            <input type="text" id="homepage-prompt" placeholder="输入测试提示词" value="创建一个简单的网站">
            <br>
            <button onclick="testHomepagePreCheck()">测试首页积分预检查</button>
            <div id="homepage-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 模拟首页发送消息</h3>
            <p style="color: #666;">模拟用户在首页输入框中发送消息的完整流程</p>
            <input type="text" id="simulate-prompt" placeholder="输入模拟消息" value="5858写在页面中间即可">
            <br>
            <button onclick="simulateHomepageSubmit()">模拟首页发送消息</button>
            <div id="simulate-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 检查控制台错误</h3>
            <p style="color: #666;">检查浏览器控制台是否有积分不足相关的错误</p>
            <button onclick="checkConsoleErrors()">检查控制台状态</button>
            <div id="console-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 1. 测试积分余额查询
        async function testPointsBalance() {
            try {
                showResult('balance-result', '正在查询积分余额...', 'info');
                
                const response = await fetch('/api/points/balance');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const result = `✅ 积分余额查询成功
总积分: ${data.data.totalPoints}
详细余额: ${JSON.stringify(data.data.summary, null, 2)}`;
                    showResult('balance-result', result, 'success');
                } else {
                    showResult('balance-result', `❌ 查询失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('balance-result', `❌ 查询出错: ${error.message}`, 'error');
            }
        }

        // 2. 测试首页积分预检查
        async function testHomepagePreCheck() {
            try {
                const prompt = document.getElementById('homepage-prompt').value;
                showResult('homepage-result', '正在进行首页积分预检查...', 'info');
                
                const response = await fetch('/api/ask-ai', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: [],
                        preCheckOnly: true
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 402) {
                    const result = `⚠️ 首页积分不足检测正常
状态码: ${response.status}
需要积分: ${data.pointsRequired}
当前积分: ${data.currentPoints}
错误信息: ${data.message}

✅ 这是正常的积分不足响应`;
                    showResult('homepage-result', result, 'info');
                } else if (response.ok) {
                    const result = `✅ 首页积分检查通过
积分消耗: ${data.pointsCost}
当前积分: ${data.currentPoints}
消息: ${data.message}`;
                    showResult('homepage-result', result, 'success');
                } else {
                    showResult('homepage-result', `❌ 首页预检查失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('homepage-result', `❌ 首页预检查出错: ${error.message}`, 'error');
            }
        }

        // 3. 模拟首页发送消息
        async function simulateHomepageSubmit() {
            try {
                const prompt = document.getElementById('simulate-prompt').value;
                showResult('simulate-result', '正在模拟首页发送消息...', 'info');
                
                // 记录控制台错误
                const originalConsoleError = console.error;
                const consoleErrors = [];
                console.error = function(...args) {
                    consoleErrors.push(args.join(' '));
                    originalConsoleError.apply(console, args);
                };
                
                // 模拟首页发送逻辑
                const response = await fetch('/api/ask-ai', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: prompt,
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: [],
                        preCheckOnly: true
                    })
                });
                
                // 恢复原始console.error
                console.error = originalConsoleError;
                
                const data = await response.json();
                
                if (response.status === 402) {
                    const result = `✅ 模拟首页发送消息测试结果：

积分不足检测: ✅ 正常
状态码: ${response.status}
需要积分: ${data.pointsRequired}
当前积分: ${data.currentPoints}

期望行为:
- 显示友好的蓝色信息提示 💰
- 消息保留在输入框中
- 不显示红色错误
- 不在控制台报错

控制台错误数量: ${consoleErrors.length}
${consoleErrors.length > 0 ? '⚠️ 发现控制台错误:\n' + consoleErrors.join('\n') : '✅ 无控制台错误'}`;
                    
                    showResult('simulate-result', result, consoleErrors.length > 0 ? 'error' : 'success');
                } else if (response.ok) {
                    showResult('simulate-result', `✅ 积分充足，可以正常发送消息`, 'success');
                } else {
                    showResult('simulate-result', `❌ 模拟发送失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('simulate-result', `❌ 模拟发送出错: ${error.message}`, 'error');
            }
        }

        // 4. 检查控制台错误
        function checkConsoleErrors() {
            const result = `📊 控制台状态检查:

当前时间: ${new Date().toLocaleString()}

检查要点:
1. 是否有积分不足相关的红色错误
2. 是否有 "Error: 需要 X 积分，当前余额 Y 积分" 错误
3. 是否有未捕获的异常

✅ 正确的行为:
- 积分不足时显示蓝色信息提示
- 控制台只有正常的日志信息
- 没有红色错误信息

请手动检查浏览器控制台 (F12) 来验证是否有错误。`;
            
            showResult('console-result', result, 'info');
        }

        // 页面加载时自动查询积分余额
        window.addEventListener('load', function() {
            testPointsBalance();
        });
    </script>
</body>
</html>

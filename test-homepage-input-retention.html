<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页输入框保留测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .expected-behavior {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 首页输入框保留测试</h1>
        <p>测试积分不足时首页输入框是否正确保留用户输入的内容</p>

        <div class="expected-behavior">
            <h4>📋 期望的行为：</h4>
            <ol>
                <li><strong>积分充足时</strong>：输入框清空，跳转到编辑器</li>
                <li><strong>积分不足时</strong>：
                    <ul>
                        <li>显示友好的蓝色信息提示：💰 需要 X 积分，当前余额 Y 积分</li>
                        <li><strong>关键：消息保留在输入框中</strong></li>
                        <li>用户可以继续编辑或充值后重新发送</li>
                        <li>不显示红色错误，不在控制台报错</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 测试步骤</h3>
            <p>请按照以下步骤进行测试：</p>
            <ol>
                <li>确保当前用户积分为0或不足（可以通过下面的按钮查询）</li>
                <li>在首页输入框中输入测试消息</li>
                <li>点击发送按钮</li>
                <li>观察结果：
                    <ul>
                        <li>是否显示蓝色信息提示</li>
                        <li>输入框中的消息是否保留</li>
                        <li>控制台是否有错误</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="test-section">
            <h3>1. 查询当前积分</h3>
            <button onclick="checkPoints()">查询积分余额</button>
            <div id="points-result" style="margin-top: 10px;"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试消息建议</h3>
            <p>建议使用以下测试消息：</p>
            <ul>
                <li>"创建一个简单的网站"</li>
                <li>"5858写在页面中间即可"</li>
                <li>"制作一个蓝色主题的登录页面"</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>3. 检查控制台</h3>
            <button onclick="checkConsole()">检查控制台状态</button>
            <div id="console-result" style="margin-top: 10px;"></div>
        </div>

        <div class="test-section">
            <h3>4. 模拟测试</h3>
            <p>如果无法在首页直接测试，可以使用以下模拟测试：</p>
            <button onclick="simulateTest()">模拟积分不足场景</button>
            <div id="simulate-result" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `test-section ${type}`;
        }

        async function checkPoints() {
            try {
                showResult('points-result', '正在查询积分余额...', 'info');
                
                const response = await fetch('/api/points/balance');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const totalPoints = data.data.totalPoints;
                    const result = `
                        <h4>✅ 积分余额查询成功</h4>
                        <p><strong>总积分：${totalPoints}</strong></p>
                        <p>详细余额：</p>
                        <pre>${JSON.stringify(data.data.summary, null, 2)}</pre>
                        ${totalPoints === 0 ? 
                            '<p style="color: #dc3545;"><strong>⚠️ 当前积分为0，适合测试积分不足场景</strong></p>' : 
                            '<p style="color: #28a745;"><strong>✅ 当前有积分，可能无法测试积分不足场景</strong></p>'
                        }
                    `;
                    showResult('points-result', result, 'success');
                } else {
                    showResult('points-result', `❌ 查询失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('points-result', `❌ 查询出错: ${error.message}`, 'error');
            }
        }

        function checkConsole() {
            const result = `
                <h4>📊 控制台检查指南</h4>
                <p>请打开浏览器开发者工具 (F12)，检查控制台中是否有以下内容：</p>
                
                <h5>✅ 正常的日志（应该看到）：</h5>
                <ul>
                    <li>🔍 WelcomeLayout: 预检查发现积分不足</li>
                    <li>⚠️ WelcomeLayout: 积分不足，消息保留在输入框，不继续处理</li>
                    <li>其他蓝色或绿色的信息日志</li>
                </ul>
                
                <h5>❌ 错误信息（不应该看到）：</h5>
                <ul>
                    <li style="color: #dc3545;">Error: 需要 X 积分，当前余额 Y 积分</li>
                    <li style="color: #dc3545;">❌ WelcomeLayout: AI请求失败</li>
                    <li style="color: #dc3545;">❌ WelcomeLayout: 项目创建失败</li>
                    <li>任何红色的错误信息</li>
                </ul>
                
                <p><strong>如果看到红色错误，说明修复还有问题需要进一步调整。</strong></p>
            `;
            showResult('console-result', result, 'info');
        }

        async function simulateTest() {
            try {
                showResult('simulate-result', '正在模拟积分不足场景...', 'info');
                
                // 模拟预检查请求
                const response = await fetch('/api/ask-ai', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: '测试消息：创建一个简单的网站',
                        provider: 'deepseek-official',
                        model: 'deepseek-chat',
                        images: [],
                        preCheckOnly: true
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 402) {
                    const result = `
                        <h4>✅ 模拟测试结果</h4>
                        <p><strong>积分不足检测正常工作</strong></p>
                        <ul>
                            <li>状态码: ${response.status} (Payment Required)</li>
                            <li>需要积分: ${data.pointsRequired}</li>
                            <li>当前积分: ${data.currentPoints}</li>
                            <li>错误信息: ${data.message}</li>
                        </ul>
                        
                        <h5>期望的用户体验：</h5>
                        <ul>
                            <li>✅ 显示蓝色信息提示：💰 需要 ${data.pointsRequired} 积分，当前余额 ${data.currentPoints} 积分</li>
                            <li>✅ 输入框中的消息保留不变</li>
                            <li>✅ 提供"立即充值"按钮</li>
                            <li>✅ 不显示红色错误</li>
                        </ul>
                        
                        <p><strong>请在首页实际测试以验证输入框保留功能。</strong></p>
                    `;
                    showResult('simulate-result', result, 'success');
                } else if (response.ok) {
                    const result = `
                        <h4>ℹ️ 积分充足</h4>
                        <p>当前用户积分充足，无法模拟积分不足场景。</p>
                        <p>如需测试积分不足功能，请：</p>
                        <ul>
                            <li>联系管理员调整积分</li>
                            <li>或等待积分消耗完毕后再测试</li>
                        </ul>
                    `;
                    showResult('simulate-result', result, 'info');
                } else {
                    showResult('simulate-result', `❌ 模拟测试失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('simulate-result', `❌ 模拟测试出错: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动查询积分
        window.addEventListener('load', function() {
            checkPoints();
        });
    </script>
</body>
</html>

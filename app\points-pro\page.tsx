"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/loomrunhooks/useUser";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Header } from "@/components/editor/header";
import {
  Coins,
  TrendingUp,
  TrendingDown,
  History,
  RefreshCw,
  Clock,
  Gift,
  Zap,
  Download,
  Users,
  CreditCard,
  Calendar,
  AlertCircle,
  Settings,
  Award,
  Target,
  Filter,
  Phone,
  UserPlus
} from "lucide-react";
import { toast } from "sonner";

interface PointsTransaction {
  id: number;
  transaction_type: 'earn' | 'spend';
  points_amount: number;
  balance_before: number;
  balance_after: number;
  source_type: string;
  points_type: string;
  description: string;
  displayDescription?: string; // 用户友好的描述
  displayModelName?: string;   // 用户友好的模型名称
  expires_at: string | null;
  created_at: string;
  metadata?: any;
  isExpired?: boolean;
  isExpiringSoon?: boolean;
  formattedAmount?: string;
  formattedDate?: string;
  formattedExpiry?: string | null;
}

interface ConsumptionRecord {
  id: number;
  user_id: number;
  transaction_id: number;
  balance_record_id: number;
  points_consumed: number;
  points_type: 'activity' | 'subscription' | 'recharge';
  consumed_at: string;
  source_type: string;
  transaction_description: string;
  displayTransactionDescription?: string; // 用户友好的交易描述
  displayModelName?: string;              // 用户友好的模型名称
  original_expires_at: string | null;
  wasExpired: boolean;
  formattedConsumedAmount: string;
  formattedConsumedDate: string;
  formattedOriginalExpiry: string;
}

interface PointsBalance {
  id: number;
  points_type: 'activity' | 'subscription' | 'recharge';
  points_amount: number;
  expires_at: string | null;
  created_at: string;
}

interface PointsSummary {
  points_type: 'activity' | 'subscription' | 'recharge';
  total_points: number;
  record_count: number;
  earliest_expiry: string | null;
}

export default function PointsProPage() {
  const { user, loading } = useUser();
  const router = useRouter();
  const [transactions, setTransactions] = useState<PointsTransaction[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'earn' | 'spend'>('all');
  const [pointsSummary, setPointsSummary] = useState<PointsSummary[]>([]);
  const [balancesLoading, setBalancesLoading] = useState(false);
  const [consumptionRecords, setConsumptionRecords] = useState<ConsumptionRecord[]>([]);
  const [consumptionLoading, setConsumptionLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'transactions' | 'consumption'>('transactions');
  const [filters, setFilters] = useState({
    pointsType: '',
    sourceType: '',
    dateFrom: '',
    dateTo: ''
  });

  // 获取积分历史（支持筛选）
  const fetchTransactions = async (customFilters?: any) => {
    setTransactionsLoading(true);
    try {
      const currentFilters = customFilters || filters;
      const params = new URLSearchParams({
        limit: '50',
        type: selectedCategory === 'all' ? '' : selectedCategory,
        ...(currentFilters.pointsType && { points_type: currentFilters.pointsType }),
        ...(currentFilters.sourceType && { source_type: currentFilters.sourceType }),
        ...(currentFilters.dateFrom && { date_from: currentFilters.dateFrom }),
        ...(currentFilters.dateTo && { date_to: currentFilters.dateTo })
      });

      const response = await fetch(`/api/points/history?${params}`);
      const data = await response.json();
      if (data.success) {
        setTransactions(data.data.history);
      } else {
        toast.error('获取积分历史失败');
      }
    } catch (error) {
      console.error('获取积分历史失败:', error);
      toast.error('获取积分历史失败');
    } finally {
      setTransactionsLoading(false);
    }
  };

  // 获取积分消费记录
  const fetchConsumptionRecords = async (customFilters?: any) => {
    setConsumptionLoading(true);
    try {
      const currentFilters = customFilters || filters;
      const params = new URLSearchParams({
        limit: '50',
        ...(currentFilters.pointsType && { points_type: currentFilters.pointsType }),
        ...(currentFilters.dateFrom && { date_from: currentFilters.dateFrom }),
        ...(currentFilters.dateTo && { date_to: currentFilters.dateTo })
      });

      const response = await fetch(`/api/points/consumption?${params}`);
      const data = await response.json();
      if (data.success) {
        setConsumptionRecords(data.data.consumptionRecords);
      } else {
        toast.error('获取消费记录失败');
      }
    } catch (error) {
      console.error('获取消费记录失败:', error);
      toast.error('获取消费记录失败');
    } finally {
      setConsumptionLoading(false);
    }
  };

  // 获取积分余额详情
  const fetchPointsBalance = async () => {
    setBalancesLoading(true);
    try {
      const response = await fetch('/api/points/balance');
      const data = await response.json();
      if (data.success) {
        setPointsSummary(data.data.summary || []);
      } else {
        toast.error('获取积分余额失败');
      }
    } catch (error) {
      console.error('获取积分余额失败:', error);
      toast.error('获取积分余额失败');
    } finally {
      setBalancesLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchTransactions();
      fetchPointsBalance();
      if (activeTab === 'consumption') {
        fetchConsumptionRecords();
      }
    }
  }, [user, selectedCategory, activeTab]);

  // 处理筛选条件变化
  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
    if (activeTab === 'transactions') {
      fetchTransactions(newFilters);
    } else {
      fetchConsumptionRecords(newFilters);
    }
  };

  // 如果用户未登录，重定向到首页
  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [user, loading, router]);

  const getSourceTypeLabel = (sourceType: string) => {
    const labels: Record<string, string> = {
      registration: '注册奖励',
      ai_request: 'AI请求',
      export: '项目导出',
      invitation: '邀请奖励',
      recharge: '充值',
      subscription: '订阅',
      admin_adjust: '管理员调整',
      refund: '退款'
    };
    return labels[sourceType] || sourceType;
  };

  const getSourceTypeIcon = (sourceType: string) => {
    const icons: Record<string, React.ReactNode> = {
      registration: <Gift className="w-4 h-4" />,
      ai_request: <Zap className="w-4 h-4" />,
      export: <Download className="w-4 h-4" />,
      invitation: <Users className="w-4 h-4" />,
      recharge: <CreditCard className="w-4 h-4" />,
      subscription: <Calendar className="w-4 h-4" />,
      admin_adjust: <AlertCircle className="w-4 h-4" />,
      refund: <RefreshCw className="w-4 h-4" />
    };
    return icons[sourceType] || <Coins className="w-4 h-4" />;
  };

  const getPointsTypeColor = (pointsType: string) => {
    const colors: Record<string, string> = {
      activity: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      subscription: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      recharge: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
    };
    return colors[pointsType] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  const getPointsTypeLabel = (pointsType: string) => {
    const labels: Record<string, string> = {
      activity: '活动积分',
      subscription: '订阅积分',
      recharge: '充值积分'
    };
    return labels[pointsType] || pointsType;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 过滤交易记录
  const filteredTransactions = transactions.filter(transaction => {
    if (selectedCategory === 'all') return true;
    return transaction.transaction_type === selectedCategory;
  });

  // 获取积分类型的显示信息
  const getPointsTypeInfo = (pointsType: 'activity' | 'subscription' | 'recharge') => {
    const typeInfo = {
      activity: {
        label: '活动积分',
        color: 'from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50',
        borderColor: 'border-green-200 dark:border-green-800',
        textColor: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-600',
        icon: <Gift className="w-4 h-4 text-white" />
      },
      subscription: {
        label: '订阅积分',
        color: 'from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50',
        borderColor: 'border-blue-200 dark:border-blue-800',
        textColor: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-600',
        icon: <Calendar className="w-4 h-4 text-white" />
      },
      recharge: {
        label: '充值积分',
        color: 'from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50',
        borderColor: 'border-purple-200 dark:border-purple-800',
        textColor: 'text-purple-600 dark:text-purple-400',
        bgColor: 'bg-purple-600',
        icon: <CreditCard className="w-4 h-4 text-white" />
      }
    };
    return typeInfo[pointsType];
  };



  // 格式化有效期显示
  const formatExpiryDate = (expiresAt: string | null) => {
    if (!expiresAt) return '永久有效';
    const expireDate = new Date(expiresAt);
    const now = new Date();
    const diffDays = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays <= 0) return '已过期';
    if (diffDays <= 7) return `${diffDays}天后过期`;
    return expireDate.toLocaleDateString('zh-CN');
  };

  // 检查是否即将过期
  const isExpiringSoon = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    const expireDate = new Date(expiresAt);
    const now = new Date();
    const diffDays = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays > 0;
  };

  if (loading) {
    return (
      <div className="h-screen bg-background flex flex-col">
        <Header onLogoClick={() => router.push('/')} />
        <div className="flex-1 flex items-center justify-center">
          <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* 使用系统的 Header 组件 */}
      <Header onLogoClick={() => router.push('/')} />
      
      {/* 主布局区域 - 左侧工具栏 + 右侧内容区 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧工具栏 */}
        <div className="w-80 bg-background border-r border-border flex flex-col">
          {/* 工具栏标题 */}
          <div className="p-6 border-b border-border">
            <h2 className="text-xl font-semibold flex items-center gap-3">
              <Coins className="w-6 h-6 text-blue-600" />
              积分中心
            </h2>
            <p className="text-sm text-muted-foreground mt-1">管理您的积分资产</p>
          </div>
          
          {/* 积分概览 */}
          <div className="p-6 space-y-4">
            {/* 总积分 */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">当前总积分</p>
                  <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{user.points?.toLocaleString() || 0}</p>
                </div>
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <Coins className="w-5 h-5 text-white" />
                </div>
              </div>
            </div>

            {/* 积分类型详情 */}
            {balancesLoading ? (
              <div className="flex items-center justify-center py-4">
                <RefreshCw className="w-5 h-5 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="space-y-3">
                {pointsSummary.length > 0 ? (
                  pointsSummary.map((summary) => {
                    const typeInfo = getPointsTypeInfo(summary.points_type);
                    return (
                      <div key={summary.points_type} className={`bg-gradient-to-br ${typeInfo.color} border ${typeInfo.borderColor} rounded-lg p-3`}>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className={`w-6 h-6 ${typeInfo.bgColor} rounded-full flex items-center justify-center`}>
                              {typeInfo.icon}
                            </div>
                            <p className={`text-xs font-medium ${typeInfo.textColor}`}>{typeInfo.label}</p>
                          </div>
                          {summary.earliest_expiry && isExpiringSoon(summary.earliest_expiry) && (
                            <Badge variant="destructive" className="text-xs px-1 py-0">
                              即将过期
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <p className={`text-lg font-bold ${typeInfo.textColor.replace('dark:text-', 'dark:text-').replace('text-', 'text-')}`}>
                            {summary.total_points.toLocaleString()}
                          </p>
                          <div className="text-right">
                            <p className="text-xs text-muted-foreground">
                              {summary.record_count} 笔记录
                            </p>
                            {summary.earliest_expiry && (
                              <p className={`text-xs ${isExpiringSoon(summary.earliest_expiry) ? 'text-red-600' : 'text-muted-foreground'}`}>
                                {formatExpiryDate(summary.earliest_expiry)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    <Coins className="w-8 h-8 mx-auto mb-2 opacity-30" />
                    <p className="text-sm">暂无积分余额</p>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* 筛选选项 */}
          <div className="p-6 border-t border-border">
            <h3 className="text-sm font-semibold text-foreground mb-3">记录筛选</h3>
            <div className="space-y-2">
              <Button 
                variant={selectedCategory === 'all' ? 'default' : 'ghost'} 
                className="w-full justify-start text-sm"
                onClick={() => setSelectedCategory('all')}
              >
                <History className="w-4 h-4 mr-2" />
                全部记录
              </Button>
              <Button 
                variant={selectedCategory === 'earn' ? 'default' : 'ghost'} 
                className="w-full justify-start text-sm"
                onClick={() => setSelectedCategory('earn')}
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                获得记录
              </Button>
              <Button 
                variant={selectedCategory === 'spend' ? 'default' : 'ghost'} 
                className="w-full justify-start text-sm"
                onClick={() => setSelectedCategory('spend')}
              >
                <TrendingDown className="w-4 h-4 mr-2" />
                消费记录
              </Button>
            </div>
          </div>
          
          {/* 快捷操作 */}
          <div className="p-6 border-t border-border mt-auto">
            <h3 className="text-sm font-semibold text-foreground mb-3">快捷操作</h3>
            <div className="space-y-2">
              <Button
                variant="ghost"
                className="w-full justify-start text-sm"
                onClick={() => {
                  fetchTransactions();
                  fetchPointsBalance();
                  if (activeTab === 'consumption') {
                    fetchConsumptionRecords();
                  }
                }}
                disabled={transactionsLoading || balancesLoading || consumptionLoading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${(transactionsLoading || balancesLoading || consumptionLoading) ? 'animate-spin' : ''}`} />
                刷新数据
              </Button>
              <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => router.push('/recharge')}>
                <CreditCard className="w-4 h-4 mr-2" />
                充值积分
              </Button>
              <Button variant="ghost" className="w-full justify-start text-sm" onClick={() => router.push('/invite')}>
                <Users className="w-4 h-4 mr-2" />
                邀请好友
              </Button>
            </div>
          </div>
        </div>
        
        {/* 右侧内容区 */}
        <div className="flex-1 overflow-y-auto bg-muted/30">
          <div className="p-8">
            {/* 标题和标签页 */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold flex items-center gap-3 mb-4">
                <History className="w-6 h-6 text-foreground" />
                积分变动详情
                <Badge variant="secondary" className="ml-2">
                  {activeTab === 'transactions' ? transactions.length : consumptionRecords.length} 条记录
                </Badge>
              </h1>

              {/* 标签页切换 */}
              <div className="flex items-center gap-1 bg-muted rounded-lg p-1 mb-4">
                <button
                  onClick={() => setActiveTab('transactions')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'transactions'
                      ? 'bg-background text-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  变更记录
                </button>
                <button
                  onClick={() => setActiveTab('consumption')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'consumption'
                      ? 'bg-background text-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <TrendingDown className="w-4 h-4 mr-2" />
                  消耗详情
                </button>
              </div>

              <p className="text-muted-foreground">
                {activeTab === 'transactions' && '查看您的积分获得和消费变更记录'}
                {activeTab === 'consumption' && '查看详细的积分消耗记录和来源'}
              </p>
            </div>

            {/* 筛选器 */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <Filter className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">筛选条件</span>
              </div>

              {activeTab === 'transactions' && (
                <div className="flex items-center gap-2 mb-3">
                  <Button
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('all')}
                    className="text-xs"
                  >
                    全部
                  </Button>
                  <Button
                    variant={selectedCategory === 'earn' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('earn')}
                    className="text-xs"
                  >
                    <TrendingUp className="w-3 h-3 mr-1" />
                    获得
                  </Button>
                  <Button
                    variant={selectedCategory === 'spend' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('spend')}
                    className="text-xs"
                  >
                    <TrendingDown className="w-3 h-3 mr-1" />
                    消费
                  </Button>
                </div>
              )}

              {/* 高级筛选 */}
              <div className="grid grid-cols-2 gap-3">
                <select
                  value={filters.pointsType}
                  onChange={(e) => handleFilterChange({ ...filters, pointsType: e.target.value })}
                  className="px-3 py-2 text-xs border rounded-md bg-background"
                >
                  <option value="">所有积分类型</option>
                  <option value="activity">活动积分</option>
                  <option value="subscription">订阅积分</option>
                  <option value="recharge">充值积分</option>
                </select>

                {activeTab === 'transactions' && (
                  <select
                    value={filters.sourceType}
                    onChange={(e) => handleFilterChange({ ...filters, sourceType: e.target.value })}
                    className="px-3 py-2 text-xs border rounded-md bg-background"
                  >
                    <option value="">所有来源</option>
                    <option value="registration">注册奖励</option>
                    <option value="ai_request">AI请求</option>
                    <option value="export">项目导出</option>
                    <option value="invitation">邀请奖励</option>
                    <option value="recharge">充值</option>
                    <option value="subscription">订阅</option>
                    <option value="admin_adjust">管理员调整</option>
                  </select>
                )}
              </div>
            </div>

            {/* 内容区域 */}
            <div className="bg-background rounded-lg border border-border">
              <div className="p-6">
                {activeTab === 'transactions' ? (
                  // 变更记录标签页
                  transactionsLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : transactions.length > 0 ? (
                    <div className="space-y-3">
                      {transactions
                        .filter(transaction => {
                          if (selectedCategory === 'all') return true;
                          return transaction.transaction_type === selectedCategory;
                        })
                        .map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                          <div className="flex items-center gap-4">
                            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              transaction.transaction_type === 'earn'
                                ? 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400'
                                : 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                            }`}>
                              {getSourceTypeIcon(transaction.source_type)}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">{transaction.displayDescription || transaction.description}</div>
                              <div className="text-xs text-muted-foreground flex items-center gap-2 mt-1">
                                <span>{getSourceTypeLabel(transaction.source_type)}</span>
                                {transaction.points_type && (
                                  <Badge className={getPointsTypeColor(transaction.points_type)} variant="secondary">
                                    {getPointsTypeLabel(transaction.points_type)}
                                  </Badge>
                                )}
                                {transaction.isExpiringSoon && (
                                  <Badge variant="destructive" className="text-xs">
                                    即将过期
                                  </Badge>
                                )}
                                {transaction.isExpired && (
                                  <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">
                                    已过期
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-lg font-bold ${
                              transaction.transaction_type === 'earn' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.formattedAmount || (transaction.transaction_type === 'earn' ? '+' : '-') + transaction.points_amount}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              余额: {transaction.balance_after}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {transaction.formattedDate || formatDate(transaction.created_at)}
                            </div>
                            {transaction.expires_at && (
                              <div className={`text-xs flex items-center gap-1 mt-1 ${
                                transaction.isExpiringSoon ? 'text-red-600' :
                                transaction.isExpired ? 'text-gray-500' : 'text-muted-foreground'
                              }`}>
                                <Clock className="w-3 h-3" />
                                {transaction.formattedExpiry || new Date(transaction.expires_at).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      <History className="w-16 h-16 mx-auto mb-4 opacity-30" />
                      <p className="text-lg font-medium mb-2">暂无积分变更记录</p>
                      <p className="text-sm">完成任务或充值后，记录将显示在这里</p>
                    </div>
                  )
                ) : (
                  // 消耗详情标签页
                  consumptionLoading ? (
                    <div className="flex items-center justify-center py-12">
                      <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : consumptionRecords.length > 0 ? (
                    <div className="space-y-3">
                      {consumptionRecords.map((record) => (
                        <div key={record.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                          <div className="flex items-center gap-4">
                            <div className="w-10 h-10 rounded-full flex items-center justify-center bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400">
                              {getSourceTypeIcon(record.source_type)}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">{record.displayTransactionDescription || record.transaction_description}</div>
                              <div className="text-xs text-muted-foreground flex items-center gap-2 mt-1">
                                <span>{getSourceTypeLabel(record.source_type)}</span>
                                <Badge className={getPointsTypeColor(record.points_type)} variant="secondary">
                                  {getPointsTypeLabel(record.points_type)}
                                </Badge>
                                {record.wasExpired && (
                                  <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-600">
                                    过期消耗
                                  </Badge>
                                )}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                原始有效期: {record.formattedOriginalExpiry}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-red-600">
                              {record.formattedConsumedAmount}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {record.formattedConsumedDate}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              余额记录ID: {record.balance_record_id}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      <TrendingDown className="w-16 h-16 mx-auto mb-4 opacity-30" />
                      <p className="text-lg font-medium mb-2">暂无积分消耗记录</p>
                      <p className="text-sm">使用积分后，详细消耗记录将显示在这里</p>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
